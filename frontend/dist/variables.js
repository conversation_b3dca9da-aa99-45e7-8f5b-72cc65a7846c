// Variables Page JavaScript
(function($) {
    'use strict';

    var CCBVariables = {
        data: ccb_variables_data || {},
        currentVariable: {
            id: null,
            name: '',
            value: 0,
            description: ''
        },
        deleteVariableId: null,

        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            var self = this;

            // Add Variable buttons
            $('#ccb-add-variable-btn, #ccb-add-first-variable-btn').on('click', function(e) {
                e.preventDefault();
                self.showAddModal();
            });

            // Edit Variable buttons
            $(document).on('click', '.ccb-edit-variable', function(e) {
                e.preventDefault();
                var variableId = $(this).data('variable-id');
                self.editVariable(variableId);
            });

            // Delete Variable buttons
            $(document).on('click', '.ccb-delete-variable', function(e) {
                e.preventDefault();
                var variableId = $(this).data('variable-id');
                self.showDeleteModal(variableId);
            });

            // Modal close buttons
            $('.ccb-modal-close, #ccb-modal-cancel, #ccb-delete-cancel').on('click', function() {
                self.closeModals();
            });

            // Modal save button
            $('#ccb-modal-save').on('click', function() {
                self.saveVariable();
            });

            // Delete confirm button
            $('#ccb-delete-confirm').on('click', function() {
                self.deleteVariable();
            });

            // Debug table button
            $('#ccb-debug-table-btn').on('click', function(e) {
                e.preventDefault();
                self.debugTable();
            });

            // Variable name input for preview
            $('#ccb-variable-name').on('input', function() {
                self.updatePreview();
            });

            // Close modal when clicking outside
            $('.ccb-modal').on('click', function(e) {
                if (e.target === this) {
                    self.closeModals();
                }
            });
        },

        showAddModal: function() {
            this.resetForm();
            $('#ccb-modal-title').text(this.data.translations.add_variable);
            $('#ccb-variable-modal').show();
        },

        editVariable: function(variableId) {
            var variable = this.findVariableById(variableId);
            if (variable) {
                this.currentVariable = {
                    id: variable.id,
                    name: variable.name,
                    value: variable.value,
                    description: variable.description || ''
                };

                $('#ccb-variable-id').val(variable.id);
                $('#ccb-variable-name').val(variable.name).prop('disabled', true);
                $('#ccb-variable-value').val(variable.value);
                $('#ccb-variable-description').val(variable.description || '');
                $('#ccb-modal-title').text(this.data.translations.edit_variable);
                this.updatePreview();
                $('#ccb-variable-modal').show();
            }
        },

        showDeleteModal: function(variableId) {
            this.deleteVariableId = variableId;
            $('#ccb-delete-modal').show();
        },

        closeModals: function() {
            $('.ccb-modal').hide();
            this.resetForm();
        },

        resetForm: function() {
            $('#ccb-variable-form')[0].reset();
            $('#ccb-variable-id').val('');
            $('#ccb-variable-name').prop('disabled', false);
            $('#ccb-variable-preview-row').hide();
            this.currentVariable = {
                id: null,
                name: '',
                value: 0,
                description: ''
            };
        },

        updatePreview: function() {
            var name = $('#ccb-variable-name').val();
            if (name && this.isValidVariableName(name)) {
                $('#ccb-variable-preview').text('$' + name);
                $('#ccb-variable-preview-row').show();
            } else {
                $('#ccb-variable-preview-row').hide();
            }
        },

        isValidVariableName: function(name) {
            return /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(name);
        },

        saveVariable: function() {
            var self = this;
            var name = $('#ccb-variable-name').val();
            var value = $('#ccb-variable-value').val();
            var description = $('#ccb-variable-description').val();

            // Validation
            if (!name || !value) {
                alert('Please fill in all required fields.');
                return;
            }

            if (!this.isValidVariableName(name)) {
                alert('Invalid variable name. Use only letters, numbers, and underscores. Must start with a letter or underscore.');
                return;
            }

            this.showLoader();

            $.ajax({
                url: this.data.ajax_url,
                type: 'POST',
                data: {
                    action: 'ccb_save_variable',
                    nonce: this.data.nonces.ccb_save_variable,
                    name: name,
                    value: value,
                    description: description
                },
                success: function(response) {
                    self.hideLoader();
                    if (response.success) {
                        self.showSuccess(response.data.message || 'Variable saved successfully');
                        self.refreshTable(response.data.variables);
                        self.closeModals();
                    } else {
                        self.showError(response.data || 'Failed to save variable');
                    }
                },
                error: function() {
                    self.hideLoader();
                    self.showError('Error saving variable');
                }
            });
        },

        deleteVariable: function() {
            var self = this;

            this.showLoader();

            $.ajax({
                url: this.data.ajax_url,
                type: 'POST',
                data: {
                    action: 'ccb_delete_variable',
                    nonce: this.data.nonces.ccb_delete_variable,
                    id: this.deleteVariableId
                },
                success: function(response) {
                    self.hideLoader();
                    if (response.success) {
                        self.showSuccess(response.data.message || 'Variable deleted successfully');
                        self.refreshTable(response.data.variables);
                        self.closeModals();
                    } else {
                        self.showError(response.data || 'Failed to delete variable');
                    }
                },
                error: function() {
                    self.hideLoader();
                    self.showError('Error deleting variable');
                }
            });
        },

        refreshTable: function(variables) {
            if (variables.length === 0) {
                location.reload(); // Reload to show empty state
            } else {
                var tbody = $('#ccb-variables-tbody');
                tbody.empty();

                variables.forEach(function(variable) {
                    var row = $('<tr data-variable-id="' + variable.id + '">');
                    row.append('<td><strong>' + variable.name + '</strong></td>');
                    row.append('<td>' + variable.value + '</td>');
                    row.append('<td>' + (variable.description || '-') + '</td>');
                    row.append('<td><code>$' + variable.name + '</code></td>');
                    row.append('<td>' +
                        '<button type="button" class="button button-small ccb-edit-variable" data-variable-id="' + variable.id + '">Edit</button> ' +
                        '<button type="button" class="button button-small button-link-delete ccb-delete-variable" data-variable-id="' + variable.id + '">Delete</button>' +
                        '</td>');
                    tbody.append(row);
                });
            }
        },

        findVariableById: function(id) {
            return this.data.variables.find(function(variable) {
                return variable.id == id;
            });
        },

        showLoader: function() {
            $('#ccb-variables-loader').show();
        },

        hideLoader: function() {
            $('#ccb-variables-loader').hide();
        },

        showSuccess: function(message) {
            alert('Success: ' + message);
        },

        showError: function(message) {
            alert('Error: ' + message);
        },

        debugTable: function() {
            var self = this;

            $.ajax({
                url: this.data.ajax_url,
                type: 'POST',
                data: {
                    action: 'ccb_debug_variables_table'
                },
                success: function(response) {
                    if (response.success) {
                        var info = response.data;
                        var message = 'Debug Information:\n\n';
                        message += 'Table Name: ' + info.table_name + '\n';
                        message += 'Table Exists: ' + info.table_exists + '\n';
                        message += 'WordPress Prefix: ' + info.wpdb_prefix + '\n';

                        if (info.row_count !== undefined) {
                            message += 'Row Count: ' + info.row_count + '\n';
                        }

                        if (info.table_created !== undefined) {
                            message += 'Table Created: ' + info.table_created + '\n';
                        }

                        if (info.last_error) {
                            message += 'Last Error: ' + info.last_error + '\n';
                        }

                        if (info.creation_error) {
                            message += 'Creation Error: ' + info.creation_error + '\n';
                        }

                        alert(message);
                    } else {
                        self.showError('Debug failed: ' + (response.data || 'Unknown error'));
                    }
                },
                error: function() {
                    self.showError('Debug request failed');
                }
            });
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        if (document.getElementById('ccb_variables_page')) {
            CCBVariables.init();
        }
    });

})(jQuery);
