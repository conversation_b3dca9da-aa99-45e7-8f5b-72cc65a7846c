# Cost Calculator Builder - Variables Feature

## Overview

The Variables feature allows you to create and manage global variables that can be used across all calculators in your Cost Calculator Builder plugin. Variables provide a centralized way to manage commonly used values like tax rates, base prices, or conversion factors.

## Features

### 1. Variables Management
- **Admin Menu**: New "Variables" submenu under Cost Calculator
- **CRUD Operations**: Create, Read, Update, Delete variables
- **Validation**: Variable names must follow naming conventions (alphanumeric + underscores, start with letter/underscore)
- **Global Scope**: Variables are available across all calculators

### 2. Formula Integration
- **Syntax**: Use `$variable_name` in formulas to reference variables
- **Real-time Processing**: Variables are replaced with their values during calculation
- **Backward Compatibility**: Existing formulas continue to work unchanged
- **Visual Helper**: Formula editor shows available variables with click-to-insert functionality

### 3. Database Structure
- **Table**: `wp_ccb_variables` (with prefix)
- **Fields**: 
  - `id` (Primary Key)
  - `name` (Unique variable name)
  - `value` (Decimal value)
  - `description` (Optional description)
  - `created_at` / `updated_at` (Timestamps)

## Usage Examples

### Creating Variables
1. Go to **Cost Calculator > Variables**
2. Click **Add Variable**
3. Enter variable details:
   - **Name**: `tax_rate` (alphanumeric + underscores only)
   - **Value**: `0.08` (for 8% tax)
   - **Description**: `Standard tax rate for calculations`

### Using Variables in Formulas
```javascript
// Basic usage
quantity_field_id_123 * $base_price

// With tax calculation
(quantity_field_id_123 * $unit_price) * (1 + $tax_rate)

// Complex formula
($material_cost + $labor_cost) * $markup_factor + $fixed_fee
```

### Variable Naming Rules
- ✅ Valid: `tax_rate`, `base_price`, `markup_factor`, `_private_var`
- ❌ Invalid: `tax-rate`, `2nd_price`, `tax rate`, `if`, `function`

## Technical Implementation

### Backend (PHP)
- **Model**: `CCBVariables` class for AJAX handling
- **Database**: `Variables` model for database operations
- **Integration**: Variables included in calculator data via `render.php`
- **AJAX Actions**: `ccb_get_variables`, `ccb_save_variable`, `ccb_delete_variable`

### Frontend (Vue.js/TypeScript)
- **Store**: Variables stored in `appStore.variables`
- **Processing**: `processFormulaVariables()` function replaces variable syntax
- **Integration**: Works with both regular formulas and repeater formulas
- **UI**: Admin interface for variable management

### Formula Processing Flow
1. **Load**: Variables loaded from database when calculator initializes
2. **Parse**: Formula parser identifies `$variable_name` patterns
3. **Replace**: Variable names replaced with actual values
4. **Calculate**: Standard calculation proceeds with resolved values

## File Structure

```
includes/
├── classes/
│   ├── CCBVariables.php              # AJAX controller
│   └── models/
│       └── Variables.php             # Database model
templates/
└── admin/
    └── pages/
        └── variables.php             # Admin interface
frontend/
├── dist/
│   └── variables.js                  # Admin page JavaScript
└── vue3/src/widget/
    ├── app/providers/stores/
    │   └── appStore.ts               # Variables storage
    └── actions/fields/composable/
        └── useFields.ts              # Formula processing
```

## Security Considerations

- **Validation**: Variable names validated against injection patterns
- **Sanitization**: All input sanitized before database storage
- **Permissions**: Only administrators can manage variables
- **Nonces**: AJAX requests protected with WordPress nonces

## Migration & Compatibility

- **Backward Compatible**: Existing calculators work without changes
- **Database Migration**: Variables table created automatically on plugin activation
- **Formula Compatibility**: Existing field references (`field_id_123`) unchanged

## Future Enhancements

1. **Variable Types**: Support for text/string variables
2. **Conditional Variables**: Variables that change based on conditions
3. **Import/Export**: Bulk variable management
4. **Variable Groups**: Organize variables into categories
5. **Formula Builder**: Visual formula builder with variable picker
6. **Variable History**: Track variable value changes over time

## Testing

To test the variables feature:

1. **Create Variables**: Add test variables via admin interface
2. **Use in Formulas**: Create calculator with formula using `$variable_name`
3. **Verify Calculation**: Ensure variables are replaced correctly
4. **Update Variables**: Change variable values and verify calculations update
5. **Delete Variables**: Ensure formulas handle missing variables gracefully

## Support

For issues or questions about the Variables feature:
- Check variable naming conventions
- Verify formula syntax (`$variable_name`)
- Ensure variables exist before using in formulas
- Check browser console for JavaScript errors
- Verify database table creation and permissions
