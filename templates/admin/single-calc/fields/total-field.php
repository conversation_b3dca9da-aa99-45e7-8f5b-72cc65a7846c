<div class="cbb-edit-field-container" v-if="open">
	<div class="ccb-edit-field-header">
		<span class="ccb-edit-field-title ccb-heading-3 ccb-bold"><?php esc_html_e( 'Formula', 'cost-calculator-builder' ); ?></span>
		<div class="ccb-field-actions">
			<button class="ccb-button default" @click="$emit( 'cancel' )"><?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?></button>
			<button class="ccb-button success" @click.prevent="save"><?php esc_html_e( 'Save', 'cost-calculator-builder' ); ?></button>
		</div>
	</div>
	<div class="ccb-grid-box">
		<div class="container">
			<div class="row">
				<div class="col-12">
					<div class="ccb-edit-field-switch">
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'element'}" @click="tab = 'element'">
							<?php esc_html_e( 'Element', 'cost-calculator-builder' ); ?>
						</div>
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'settings'}" @click="tab = 'settings'">
							<?php esc_html_e( 'Settings', 'cost-calculator-builder' ); ?>
							<span class="ccb-fields-required" v-if="errorsCount > 0">{{ errorsCount }}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container" v-show="tab === 'element'">
			<div class="row ccb-p-t-20">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Title', 'cost-calculator-builder' ); ?></span>
						<input type="text" class="ccb-heading-5 ccb-light" v-model.trim="totalField.label" placeholder="<?php esc_attr_e( 'Enter field name', 'cost-calculator-builder' ); ?>">
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15" v-if="errorMessage.length > 0">
				<div class="col-12">
					<div class="ccb-formula-message-errors">
						<p class="ccb-formula-error-message" v-for="(item) in errorMessage">
							{{ item.message }}
						</p>
					</div>
				</div>
			</div>

			<div class="row ccb-p-t-15" v-show="totalField.formulaView">
				<div class="col-12">
					<formula-view :field="field" @change="changeLegacy" v-model="totalField.legacyFormula" @error="setErrors" :id="totalField._id" :available_fields="available_fields"/>
				</div>
			</div>
			<div class="row ccb-p-t-10" v-show="!totalField.formulaView">
				<div class="col-12">
					<formula-field :field="field" @change="change" @error="setErrors" :id="totalField._id" v-model="totalField.costCalcFormula" :available_fields="available_fields" :formula_view="totalField.formulaView"/>
				</div>
			</div>

			<!-- Beautiful Variables Section -->
			<div class="row ccb-p-t-15" id="ccb-beautiful-variables-section">
				<div class="col-12">
					<div class="ccb-variables-container">
						<div class="ccb-variables-header">
							<h4 class="ccb-variables-title">
								<span class="ccb-variables-icon">🎯</span>
								Variables
							</h4>
							<div class="ccb-variables-actions">
								<button type="button" class="ccb-variables-refresh" onclick="window.refreshVariables && window.refreshVariables()" title="Refresh variables">
									<span class="ccb-refresh-icon">🔄</span>
								</button>
								<a href="<?php echo admin_url('admin.php?page=cost_calculator_builder&tab=variables'); ?>" target="_blank" class="ccb-variables-manage" title="Manage variables">
									<span class="ccb-manage-icon">⚙️</span>
									Manage
								</a>
							</div>
						</div>

						<!-- Loading State -->
						<div id="ccb-beautiful-variables-loading" class="ccb-variables-loading">
							<div class="ccb-loading-spinner">⏳</div>
							<span>Loading variables...</span>
						</div>

						<!-- Variables List -->
						<div id="ccb-beautiful-variables-list" class="ccb-variables-list" style="display: none;">
							<!-- Variables will be inserted here by JavaScript -->
						</div>

						<!-- Empty State -->
						<div id="ccb-beautiful-variables-empty" class="ccb-variables-empty" style="display: none;">
							<div class="ccb-empty-icon">📝</div>
							<h5>No variables found</h5>
							<p>Create your first variable to use in formulas</p>
							<a href="<?php echo admin_url('admin.php?page=cost_calculator_builder&tab=variables'); ?>" target="_blank" class="ccb-create-variable-btn">
								Create Your First Variable
							</a>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="container" v-show="tab === 'settings'">
			<div class="row ccb-p-t-10">
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.formulaView" />
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Show the legacy formula view ', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10" v-if="!disableFieldHiddenByDefault(totalField)">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.hidden"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Hidden by Default', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.advancedJsCalculation"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Advanced calculations', 'cost-calculator-builder' ); ?></h6>
						<span class="ccb-options-tooltip">
							<i class="ccb-icon-circle-question"></i>
							<span class="ccb-options-tooltip__text"><?php esc_html_e( 'Enable for advanced calculations using JavaScript-based formulas. With over 9 totals, performance may slow. Disable for faster basic calculations.' ); ?></span>
						</span>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.calculateHidden"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Calculate hidden by default', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6 ccb-p-t-10">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.fieldCurrency"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Add a measuring unit', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
			</div>
			<div class="row row-currency" :class="{'disabled': !totalField.fieldCurrency}">
				<div class="col-4">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Unit Symbol', 'cost-calculator-builder' ); ?></span>
						<input type="text" maxlength="18" v-model="fieldCurrency.currency" placeholder="<?php esc_attr_e( 'Enter unit symbol', 'cost-calculator-builder' ); ?>">
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Position', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.currencyPosition">
								<option value="left"><?php esc_html_e( 'Left', 'cost-calculator-builder' ); ?></option>
								<option value="right"><?php esc_html_e( 'Right', 'cost-calculator-builder' ); ?></option>
								<option value="left_with_space"><?php esc_html_e( 'Left with space', 'cost-calculator-builder' ); ?></option>
								<option value="right_with_space"><?php esc_html_e( 'Right with space', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Thousands separator', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.thousands_separator">
								<option value=","><?php esc_html_e( ' Comma ', 'cost-calculator-builder' ); ?></option>
								<option value="."><?php esc_html_e( ' Dot ', 'cost-calculator-builder' ); ?></option>
								<option value="'"><?php esc_html_e( ' Apostrophe ', 'cost-calculator-builder' ); ?></option>
								<option value=" "><?php esc_html_e( ' Space ', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-input-wrapper number">
						<span class="ccb-input-label"><?php esc_html_e( 'Number of decimals', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-input-box">
							<input type="number" name="num_after_integer" v-model="fieldCurrency.num_after_integer" min="0" max="8" placeholder="<?php esc_attr_e( 'Enter decimals', 'cost-calculator-builder' ); ?>" @blur="handleBlur">
							<span class="input-number-counter up" @click="numberCounterAction('num_after_integer')"></span>
							<span class="input-number-counter down" @click="numberCounterAction('num_after_integer', '-')"></span>
						</div>
					</div>
				</div>
				<div class="col-4">
					<div class="ccb-select-box">
						<span class="ccb-select-label"><?php esc_html_e( 'Decimal separator', 'cost-calculator-builder' ); ?></span>
						<div class="ccb-select-wrapper">
							<i class="ccb-icon-Path-3485 ccb-select-arrow"></i>
							<select class="ccb-select" v-model="fieldCurrency.decimal_separator">
								<option value=","><?php esc_html_e( ' Comma ', 'cost-calculator-builder' ); ?></option>
								<option value="."><?php esc_html_e( ' Dot ', 'cost-calculator-builder' ); ?></option>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Additional Classes', 'cost-calculator-builder' ); ?></span>
						<textarea class="ccb-heading-5 ccb-light" v-model="totalField.additionalStyles" placeholder="<?php esc_attr_e( 'Set Additional Classes', 'cost-calculator-builder' ); ?>"></textarea>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<style>
/* Beautiful Variables Container */
.ccb-variables-container {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12px;
	padding: 20px;
	margin-top: 15px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.1);
	color: white;
}

.ccb-variables-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15px;
}

.ccb-variables-title {
	margin: 0;
	font-size: 18px;
	font-weight: 600;
	display: flex;
	align-items: center;
	gap: 8px;
}

.ccb-variables-icon {
	font-size: 20px;
}

.ccb-variables-actions {
	display: flex;
	gap: 10px;
	align-items: center;
}

.ccb-variables-refresh {
	background: rgba(255,255,255,0.2);
	border: none;
	border-radius: 6px;
	padding: 8px 12px;
	color: white;
	cursor: pointer;
	transition: all 0.3s ease;
	font-size: 14px;
}

.ccb-variables-refresh:hover {
	background: rgba(255,255,255,0.3);
	transform: translateY(-1px);
}

.ccb-variables-manage {
	background: rgba(255,255,255,0.9);
	color: #667eea;
	text-decoration: none;
	padding: 8px 15px;
	border-radius: 6px;
	font-size: 13px;
	font-weight: 500;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 5px;
}

.ccb-variables-manage:hover {
	background: white;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* Loading State */
.ccb-variables-loading {
	text-align: center;
	padding: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10px;
	font-size: 14px;
}

.ccb-loading-spinner {
	font-size: 18px;
	animation: spin 2s linear infinite;
}

@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* Variables List */
.ccb-variables-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
	padding: 10px 0;
}

/* Variable Chips */
.ccb-variable-chip {
	background: rgba(255,255,255,0.9);
	color: #333;
	padding: 10px 15px;
	border-radius: 20px;
	cursor: pointer;
	font-size: 13px;
	font-weight: 500;
	transition: all 0.3s ease;
	border: 2px solid transparent;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	display: flex;
	align-items: center;
	gap: 8px;
}

.ccb-variable-chip:hover {
	background: white;
	transform: translateY(-2px);
	box-shadow: 0 4px 15px rgba(0,0,0,0.2);
	border-color: rgba(255,255,255,0.5);
}

.ccb-variable-chip:active {
	transform: translateY(0);
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ccb-variable-name {
	font-weight: 600;
	color: #5a67d8;
	font-family: monospace;
}

.ccb-variable-value {
	color: #38a169;
	font-size: 12px;
	background: rgba(56, 161, 105, 0.1);
	padding: 2px 6px;
	border-radius: 10px;
}

/* Empty State */
.ccb-variables-empty {
	text-align: center;
	padding: 30px 20px;
}

.ccb-empty-icon {
	font-size: 48px;
	margin-bottom: 15px;
	opacity: 0.7;
}

.ccb-variables-empty h5 {
	margin: 0 0 10px 0;
	font-size: 18px;
	font-weight: 600;
}

.ccb-variables-empty p {
	margin: 0 0 20px 0;
	opacity: 0.8;
	font-size: 14px;
}

.ccb-create-variable-btn {
	background: rgba(255,255,255,0.9);
	color: #667eea;
	text-decoration: none;
	padding: 12px 24px;
	border-radius: 25px;
	font-weight: 600;
	transition: all 0.3s ease;
	display: inline-block;
}

.ccb-create-variable-btn:hover {
	background: white;
	transform: translateY(-2px);
	box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Hide any old variables sections that might be rendered by Vue components */
.formula-field .variables-section,
.formula-view .variables-section,
[class*="variables"]:not(.ccb-variables-container):not(.ccb-variable-chip):not(.ccb-variables-header):not(.ccb-variables-title):not(.ccb-variables-actions):not(.ccb-variables-loading):not(.ccb-variables-list):not(.ccb-variables-empty) {
	display: none !important;
}

/* Hide any duplicate variables sections */
.ccb-variables-container ~ .variables-section,
.ccb-variables-container ~ [class*="variables"]:not(.ccb-variables-container):not(.ccb-variable-chip):not(.ccb-variables-header):not(.ccb-variables-title):not(.ccb-variables-actions):not(.ccb-variables-loading):not(.ccb-variables-list):not(.ccb-variables-empty) {
	display: none !important;
}
</style>

<script>
// Beautiful Variables System - Completely Rewritten
(function() {
	'use strict';

	console.log('🚀 Initializing Beautiful Variables System...');

	// State management
	let variablesState = {
		loaded: false,
		variables: [],
		elements: {}
	};

	// Get DOM elements
	function getElements() {
		return {
			loading: document.getElementById('ccb-beautiful-variables-loading'),
			list: document.getElementById('ccb-beautiful-variables-list'),
			empty: document.getElementById('ccb-beautiful-variables-empty'),
			section: document.getElementById('ccb-beautiful-variables-section')
		};
	}

	// Show loading state
	function showLoading() {
		const elements = getElements();
		if (elements.loading) elements.loading.style.display = 'block';
		if (elements.list) elements.list.style.display = 'none';
		if (elements.empty) elements.empty.style.display = 'none';
	}

	// Show variables list
	function showVariables() {
		const elements = getElements();
		if (elements.loading) elements.loading.style.display = 'none';
		if (elements.list) elements.list.style.display = 'block';
		if (elements.empty) elements.empty.style.display = 'none';
	}

	// Show empty state
	function showEmpty() {
		const elements = getElements();
		if (elements.loading) elements.loading.style.display = 'none';
		if (elements.list) elements.list.style.display = 'none';
		if (elements.empty) elements.empty.style.display = 'block';
	}

	// Create beautiful variable chip
	function createVariableChip(variable) {
		const chip = document.createElement('div');
		chip.className = 'ccb-variable-chip';
		chip.innerHTML = `
			<span class="ccb-variable-name">$${variable.name}</span>
			<span class="ccb-variable-value">${variable.value}</span>
		`;
		chip.title = `Click to insert $${variable.name} into formula${variable.description ? '\n' + variable.description : ''}`;

		chip.addEventListener('click', () => {
			insertVariableIntoFormula('$' + variable.name);

			// Visual feedback
			chip.style.transform = 'scale(0.95)';
			setTimeout(() => {
				chip.style.transform = '';
			}, 150);
		});

		return chip;
	}

	// Render variables
	function renderVariables(variables) {
		const elements = getElements();
		if (!elements.list) {
			console.error('❌ Variables list element not found');
			return;
		}

		// Clear existing content
		elements.list.innerHTML = '';

		if (!variables || variables.length === 0) {
			showEmpty();
			return;
		}

		// Create variable chips
		variables.forEach(variable => {
			const chip = createVariableChip(variable);
			elements.list.appendChild(chip);
		});

		showVariables();
		console.log(`✅ Rendered ${variables.length} variables successfully!`);
	}

	// Load variables from ajax_window
	function loadVariablesFromWindow() {
		console.log('🔍 Loading variables from ajax_window...');

		// Debug: Check ajax_window availability
		console.log('🔍 ajax_window available:', !!window.ajax_window);

		// Try to load from ajax_window first
		if (!window.ajax_window) {
			console.warn('⚠️ ajax_window not available');
			return false;
		}

		let variables = window.ajax_window.variables;

		// Fallback: try direct injection
		if (!variables && window.ccb_variables) {
			console.log('🔄 Using direct variables injection');
			variables = window.ccb_variables;
		}

		console.log('📊 Found variables:', variables);
		console.log('📊 Variables type:', typeof variables);
		console.log('📊 Variables length:', variables ? variables.length : 'N/A');
		console.log('📊 Variables debug info:', window.ajax_window.variables_debug);

		if (!variables) {
			console.warn('⚠️ No variables property in ajax_window');
			console.log('🔍 Available properties:', Object.keys(window.ajax_window));
			return false;
		}

		if (!Array.isArray(variables)) {
			console.warn('⚠️ Variables is not an array:', typeof variables);
			console.log('🔍 Variables content:', variables);
			return false;
		}

		if (variables.length === 0) {
			console.warn('⚠️ Variables array is empty');
			showEmpty();
			return true; // Still consider it "loaded" but empty
		}

		variablesState.variables = variables;
		variablesState.loaded = true;
		renderVariables(variables);
		return true;
	}

	// Load variables via AJAX as fallback
	function loadVariablesViaAjax() {
		console.log('📡 Loading variables via AJAX...');

		if (!window.ajax_window || !window.ajax_window.ajax_url) {
			console.error('❌ AJAX not available');
			showEmpty();
			return;
		}

		const formData = new FormData();
		formData.append('action', 'ccb_get_variables');
		if (window.ajax_window.nonces && window.ajax_window.nonces.ccb_get_variables) {
			formData.append('nonce', window.ajax_window.nonces.ccb_get_variables);
		}

		fetch(window.ajax_window.ajax_url, {
			method: 'POST',
			body: formData
		})
		.then(response => response.json())
		.then(data => {
			if (data.success && data.data && data.data.variables) {
				const variables = data.data.variables;
				console.log('✅ Variables loaded via AJAX:', variables);

				// Store in ajax_window for future use
				if (window.ajax_window) {
					window.ajax_window.variables = variables;
				}

				variablesState.variables = variables;
				variablesState.loaded = true;
				renderVariables(variables);
			} else {
				console.error('❌ Invalid AJAX response:', data);
				showEmpty();
			}
		})
		.catch(error => {
			console.error('❌ AJAX error:', error);
			showEmpty();
		});
	}

	// Function to insert variable into formula field
	function insertVariableIntoFormula(syntax) {
		console.log('🎯 Inserting variable:', syntax);

		// Try to find the formula input field
		const selectors = [
			'textarea[placeholder*="formula"]',
			'input[placeholder*="formula"]',
			'.formula-view textarea',
			'.formula-field input',
			'textarea.calc-textarea',
			'input[type="text"].ccb-heading-5',
			'[data-formula-input]',
			'.ccb-formula-input'
		];

		let formulaInput = null;
		for (let selector of selectors) {
			const elements = document.querySelectorAll(selector);
			for (let element of elements) {
				if (element && element.offsetParent !== null) { // visible element
					formulaInput = element;
					break;
				}
			}
			if (formulaInput) break;
		}

		if (formulaInput) {
			const start = formulaInput.selectionStart || 0;
			const end = formulaInput.selectionEnd || 0;
			const currentValue = formulaInput.value || '';

			const newValue = currentValue.substring(0, start) + syntax + currentValue.substring(end);

			// Update the input value
			formulaInput.value = newValue;

			// Trigger input event to update Vue model
			const event = new Event('input', { bubbles: true });
			formulaInput.dispatchEvent(event);

			// Set cursor position after the inserted variable
			setTimeout(() => {
				formulaInput.focus();
				formulaInput.setSelectionRange(start + syntax.length, start + syntax.length);
			}, 10);

			console.log('✅ Variable inserted successfully!');

			// Show success notification
			showNotification('Variable inserted: ' + syntax, 'success');
		} else {
			console.warn('⚠️ Formula input not found, copying to clipboard');

			// Fallback: copy to clipboard
			if (navigator.clipboard) {
				navigator.clipboard.writeText(syntax).then(() => {
					showNotification('Variable copied to clipboard: ' + syntax, 'info');
				});
			} else {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = syntax;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
				showNotification('Variable copied to clipboard: ' + syntax, 'info');
			}
		}
	}

	// Show notification
	function showNotification(message, type = 'info') {
		const notification = document.createElement('div');
		notification.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
			color: white;
			padding: 12px 20px;
			border-radius: 8px;
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
			z-index: 10000;
			font-size: 14px;
			max-width: 300px;
			animation: slideIn 0.3s ease-out;
		`;
		notification.textContent = message;

		// Add animation styles
		const style = document.createElement('style');
		style.textContent = `
			@keyframes slideIn {
				from { transform: translateX(100%); opacity: 0; }
				to { transform: translateX(0); opacity: 1; }
			}
		`;
		document.head.appendChild(style);

		document.body.appendChild(notification);

		// Remove after 3 seconds
		setTimeout(() => {
			notification.style.animation = 'slideIn 0.3s ease-out reverse';
			setTimeout(() => {
				if (notification.parentNode) {
					notification.parentNode.removeChild(notification);
				}
			}, 300);
		}, 3000);
	}

	// Wait for Vue to be available and add variables functionality
	const addVariablesFunctionality = () => {
		// Add getVariables computed property to Vue instances
		if (window.Vue && window.Vue.prototype) {
			// For Vue 2
			if (!window.Vue.prototype.$getVariables) {
				Object.defineProperty(window.Vue.prototype, '$getVariables', {
					get: function() {
						// Get variables from ajax_window
						if (window.ajax_window && window.ajax_window.variables) {
							return window.ajax_window.variables.map(variable => ({
								name: variable.name,
								value: variable.value,
								syntax: '$' + variable.name,
								description: variable.description
							}));
						}
						return [];
					}
				});
			}

			// Add variables to available_fields for formula validation
			if (!window.Vue.prototype.$originalAvailableFields) {
				// Store the original available_fields getter
				const originalDescriptor = Object.getOwnPropertyDescriptor(window.Vue.prototype, 'available_fields') ||
					{ get: function() { return this.$parent ? this.$parent.available_fields : []; } };

				window.Vue.prototype.$originalAvailableFields = originalDescriptor.get;

				// Override available_fields to include variables
				Object.defineProperty(window.Vue.prototype, 'available_fields', {
					get: function() {
						let fields = [];

						// Get original fields
						if (this.$originalAvailableFields) {
							fields = this.$originalAvailableFields.call(this) || [];
						}

						// Add variables as pseudo-fields
						if (window.ajax_window && window.ajax_window.variables && Array.isArray(window.ajax_window.variables)) {
							window.ajax_window.variables.forEach(variable => {
								fields.push({
									alias: '$' + variable.name,
									label: '$' + variable.name + ' (' + variable.value + ')',
									fieldName: 'variable',
									type: 'variable',
									value: variable.value,
									description: variable.description || ''
								});
							});
						}

						return fields;
					},
					configurable: true
				});
			}





			if (!window.Vue.prototype.$insertVariable) {
				window.Vue.prototype.$insertVariable = function(syntax) {
					insertVariableIntoFormula(syntax);
				};
			}
		}
	};

	// Hide old variables sections
	function hideOldVariablesSections() {
		// Hide any old variables sections that might be rendered by Vue components
		const oldSections = document.querySelectorAll(`
			.formula-field .variables-section,
			.formula-view .variables-section,
			[class*="variables"]:not(.ccb-variables-container):not(.ccb-variable-chip):not(.ccb-variables-header):not(.ccb-variables-title):not(.ccb-variables-actions):not(.ccb-variables-loading):not(.ccb-variables-list):not(.ccb-variables-empty)
		`);

		oldSections.forEach(section => {
			if (section && !section.closest('.ccb-variables-container')) {
				section.style.display = 'none';
				console.log('🚫 Hidden old variables section:', section);
			}
		});

		// Also hide any sections that appear after our beautiful section
		const beautifulSection = document.getElementById('ccb-beautiful-variables-section');
		if (beautifulSection) {
			let nextElement = beautifulSection.nextElementSibling;
			while (nextElement) {
				if (nextElement.textContent &&
					(nextElement.textContent.includes('Variables') ||
					 nextElement.textContent.includes('Loading variables'))) {
					nextElement.style.display = 'none';
					console.log('🚫 Hidden duplicate variables section:', nextElement);
				}
				nextElement = nextElement.nextElementSibling;
			}
		}
	}

	// Initialize the system
	function init() {
		console.log('🎬 Starting Beautiful Variables System initialization...');

		// Hide old sections first
		hideOldVariablesSections();

		// Initialize immediately
		initializeVariables();
		addVariablesFunctionality();

		// Retry with delays to handle Vue.js timing
		const retryIntervals = [500, 1000, 2000, 3000];
		retryIntervals.forEach(delay => {
			setTimeout(() => {
				console.log(`🔄 Retry attempt after ${delay}ms...`);
				hideOldVariablesSections(); // Hide old sections on each retry
				if (!variablesState.loaded) {
					initializeVariables();
				}
				addVariablesFunctionality();
			}, delay);
		});
	}

	// Global functions
	window.refreshVariables = refreshVariables;
	window.insertVariable = insertVariableIntoFormula;

	// Debug function
	window.testVariablesSystem = function() {
		console.log('🧪 Testing Beautiful Variables System...');
		console.log('Variables state:', variablesState);
		console.log('Elements found:', getElements());
		console.log('Window variables:', window.ccb_variables);
		console.log('Ajax window variables:', window.ajax_window?.variables);

		// Test element detection
		const elements = getElements();
		console.log('Element detection results:');
		Object.keys(elements).forEach(key => {
			console.log(`  ${key}:`, elements[key] ? '✅ Found' : '❌ Not found');
		});

		// Force a refresh
		console.log('🔄 Forcing refresh...');
		refreshVariables();
	};

	// Set up mutation observer to catch dynamically added old variables sections
	function setupMutationObserver() {
		const observer = new MutationObserver((mutations) => {
			mutations.forEach((mutation) => {
				if (mutation.type === 'childList') {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === Node.ELEMENT_NODE) {
							const element = node;
							// Check if the added element contains variables sections
							if (element.textContent &&
								(element.textContent.includes('Variables') ||
								 element.textContent.includes('Loading variables'))) {
								// Make sure it's not our beautiful section
								if (!element.closest('.ccb-variables-container') &&
									element.id !== 'ccb-beautiful-variables-section') {
									element.style.display = 'none';
									console.log('🚫 Hidden dynamically added variables section:', element);
								}
							}

							// Also check child elements
							const variableElements = element.querySelectorAll('[class*="variables"]');
							variableElements.forEach(varElement => {
								if (!varElement.closest('.ccb-variables-container')) {
									varElement.style.display = 'none';
									console.log('🚫 Hidden dynamically added child variables element:', varElement);
								}
							});
						}
					});
				}
			});
		});

		// Start observing
		observer.observe(document.body, {
			childList: true,
			subtree: true
		});

		console.log('👁️ Mutation observer set up to catch old variables sections');
	}

	// Start initialization
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', () => {
			init();
			setupMutationObserver();
		});
	} else {
		init();
		setupMutationObserver();
	}

})();

// Legacy test function for debugging
window.testVariables = function() {
	console.log('🧪 Testing variables...');
	console.log('window.ajax_window:', window.ajax_window);
	console.log('window.ajax_window.variables:', window.ajax_window?.variables);

	if (window.ajax_window && window.ajax_window.variables) {
		alert('Variables found: ' + JSON.stringify(window.ajax_window.variables, null, 2));
	} else {
		alert('No variables found in ajax_window');
	}

	// Force refresh
	if (window.refreshVariables) {
		window.refreshVariables();
	}
};




</script>
