<div class="cbb-edit-field-container">
	<div class="ccb-edit-field-header">
		<span class="ccb-edit-field-title ccb-heading-3 ccb-bold"><?php esc_html_e( 'Total field', 'cost-calculator-builder' ); ?></span>
		<div class="ccb-field-actions">
			<button class="ccb-button default" @click="$emit( 'cancel' )"><?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?></button>
			<button class="ccb-button success" @click.prevent="save(totalField, id, index, totalField.alias)"><?php esc_html_e( 'Save', 'cost-calculator-builder' ); ?></button>
		</div>
	</div>
	<div class="ccb-grid-box ccb-vertical">
		<div class="container">
			<div class="row">
				<div class="col-12">
					<div class="ccb-edit-field-switch">
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'main'}" @click="tab = 'main'">
							<?php esc_html_e( 'Element', 'cost-calculator-builder' ); ?>
							<span class="ccb-fields-required" v-if="errorsCount > 0">{{ errorsCount }}</span>
						</div>
						<div class="ccb-edit-field-switch-item ccb-default-title" :class="{active: tab === 'settings'}" @click="tab = 'settings'">
							<?php esc_html_e( 'Settings', 'cost-calculator-builder' ); ?>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="container" v-show="tab === 'main'">
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Name', 'cost-calculator-builder' ); ?></span>
						<input type="text" class="ccb-heading-5 ccb-light" placeholder="<?php esc_attr_e( 'Enter field name', 'cost-calculator-builder' ); ?>" v-model.trim="totalField.label">
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Description', 'cost-calculator-builder' ); ?></span>
						<input type="text" class="ccb-heading-5 ccb-light" placeholder="<?php esc_attr_e( 'Enter field description', 'cost-calculator-builder' ); ?>" v-model.trim="totalField.description">
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Formula', 'cost-calculator-builder' ); ?></span>
						<div class="formula-view">
							<textarea class="calc-textarea ccb-heading-5 ccb-light"
								v-model="totalField.costCalcFormula"
								placeholder="<?php esc_attr_e( 'Enter formula', 'cost-calculator-builder' ); ?>"
								:class="{'ccb-input-required': isObjectHasPath(errors, ['costCalcFormula'] ) && errors.costCalcFormula}"
								@input="errors.costCalcFormula=false">
							</textarea>
							<span class="ccb-error-tip default" v-if="isObjectHasPath(errors, ['costCalcFormula'] ) && errors.costCalcFormula" v-html="errors.costCalcFormula"></span>
						</div>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Available fields', 'cost-calculator-builder' ); ?></span>
						<div class="available-fields">
							<span class="available-field" v-for="field in available_fields" @click="insertField(field.alias)">
								{{ field.label }}
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="container" v-show="tab === 'settings'">
			<div class="row ccb-p-t-15">
				<div class="col-6">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.addToSummary"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Show in Grand Total', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
				<div class="col-6">
					<div class="list-header">
						<div class="ccb-switch">
							<input type="checkbox" v-model="totalField.hidden"/>
							<label></label>
						</div>
						<h6 class="ccb-heading-5"><?php esc_html_e( 'Hidden by Default', 'cost-calculator-builder' ); ?></h6>
					</div>
				</div>
			</div>
			<div class="row ccb-p-t-15">
				<div class="col-12">
					<div class="ccb-input-wrapper">
						<span class="ccb-input-label"><?php esc_html_e( 'Additional Classes', 'cost-calculator-builder' ); ?></span>
						<textarea class="ccb-heading-5 ccb-light" v-model="totalField.additionalStyles" placeholder="<?php esc_attr_e( 'Set Additional Classes', 'cost-calculator-builder' ); ?>"></textarea>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- VARIABLES SYSTEM V2 - BEAUTIFUL DESIGN -->
<div id="ccb-variables-system-v2" class="ccb-vars-panel">
	<div class="ccb-vars-header">
		<div class="ccb-vars-title">
			<span class="ccb-vars-icon">🔧</span>
			<h4>Variables</h4>
			<span id="ccb-vars-count" class="ccb-vars-count">0</span>
		</div>
		<div class="ccb-vars-actions">
			<button id="ccb-vars-refresh" class="ccb-vars-refresh" title="Refresh Variables">
				🔄
			</button>
			<a href="<?php echo esc_url( admin_url( 'admin.php?page=cost_calculator_builder&tab=variables' ) ); ?>"
			   class="ccb-vars-manage" target="_blank">
				⚙️ Manage
			</a>
		</div>
	</div>

	<div class="ccb-vars-content">
		<!-- Loading State -->
		<div id="ccb-vars-loading" class="ccb-vars-loading" style="display: none;">
			<span class="ccb-vars-spinner">⏳</span>
			<span>Loading variables...</span>
		</div>

		<!-- Variables Grid -->
		<div id="ccb-vars-grid" class="ccb-vars-grid" style="display: none;">
			<!-- Variables will be populated here -->
		</div>

		<!-- Empty State -->
		<div id="ccb-vars-empty" class="ccb-vars-empty" style="display: none;">
			<div class="ccb-vars-empty-icon">📝</div>
			<h5>No Variables Found</h5>
			<p>Create variables to use in your formulas</p>
			<a href="<?php echo esc_url( admin_url( 'admin.php?page=cost_calculator_builder&tab=variables' ) ); ?>"
			   class="ccb-vars-create-btn" target="_blank">
				Create Variables
			</a>
		</div>
	</div>
</div>

<style>
/* NEW VARIABLES SYSTEM V2 - CLEAN DESIGN */
.ccb-vars-panel {
	background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
	border-radius: 16px;
	padding: 24px;
	margin-top: 16px;
	box-shadow: 0 8px 32px rgba(79, 70, 229, 0.15);
	color: white;
	position: relative;
	overflow: hidden;
}

.ccb-vars-panel::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
	pointer-events: none;
}

.ccb-vars-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20px;
	position: relative;
	z-index: 1;
}

.ccb-vars-title {
	display: flex;
	align-items: center;
	gap: 12px;
}

.ccb-vars-title h4 {
	margin: 0;
	font-size: 20px;
	font-weight: 700;
	letter-spacing: -0.5px;
}

.ccb-vars-icon {
	font-size: 24px;
	filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.ccb-vars-count {
	background: rgba(255,255,255,0.2);
	padding: 4px 8px;
	border-radius: 12px;
	font-size: 12px;
	font-weight: 600;
	min-width: 24px;
	text-align: center;
}

.ccb-vars-actions {
	display: flex;
	align-items: center;
	gap: 12px;
}

.ccb-vars-refresh {
	background: rgba(255,255,255,0.15);
	border: 1px solid rgba(255,255,255,0.2);
	border-radius: 10px;
	padding: 8px 12px;
	color: white;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	font-size: 16px;
	backdrop-filter: blur(10px);
}

.ccb-vars-refresh:hover {
	background: rgba(255,255,255,0.25);
	transform: translateY(-2px) scale(1.05);
	box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.ccb-vars-manage {
	background: rgba(255,255,255,0.95);
	color: #4f46e5;
	text-decoration: none;
	padding: 8px 16px;
	border-radius: 10px;
	font-weight: 600;
	font-size: 14px;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	display: flex;
	align-items: center;
	gap: 6px;
	backdrop-filter: blur(10px);
}

.ccb-vars-manage:hover {
	background: white;
	transform: translateY(-2px);
	box-shadow: 0 6px 20px rgba(0,0,0,0.15);
	text-decoration: none;
	color: #4f46e5;
}

.ccb-vars-content {
	position: relative;
	z-index: 1;
}

.ccb-vars-loading {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12px;
	padding: 24px;
	background: rgba(255,255,255,0.1);
	border-radius: 12px;
	font-size: 16px;
	backdrop-filter: blur(10px);
}

.ccb-vars-spinner {
	font-size: 20px;
	animation: ccb-spin 1.5s linear infinite;
}

@keyframes ccb-spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.ccb-vars-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
	gap: 12px;
	margin-top: 16px;
}

.ccb-var-card {
	background: rgba(255,255,255,0.95);
	color: #1f2937;
	padding: 16px;
	border-radius: 12px;
	cursor: pointer;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: 2px solid transparent;
	backdrop-filter: blur(10px);
	position: relative;
	overflow: hidden;
}

.ccb-var-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #4f46e5, #7c3aed);
}

.ccb-var-card:hover {
	background: white;
	transform: translateY(-4px) scale(1.02);
	box-shadow: 0 12px 32px rgba(79, 70, 229, 0.2);
	border-color: rgba(255,255,255,0.8);
}

.ccb-var-card:active {
	transform: translateY(-2px) scale(1.01);
}

.ccb-var-name {
	font-weight: 700;
	font-size: 16px;
	color: #4f46e5;
	margin-bottom: 4px;
	display: flex;
	align-items: center;
	gap: 6px;
}

.ccb-var-name::before {
	content: '$';
	font-weight: 400;
	opacity: 0.7;
}

.ccb-var-value {
	font-size: 14px;
	color: #6b7280;
	font-weight: 500;
}

.ccb-var-desc {
	font-size: 12px;
	color: #9ca3af;
	margin-top: 4px;
	opacity: 0.8;
}

.ccb-vars-empty {
	text-align: center;
	padding: 40px 24px;
	background: rgba(255,255,255,0.1);
	border-radius: 12px;
	margin-top: 16px;
	backdrop-filter: blur(10px);
}

.ccb-vars-empty-icon {
	font-size: 48px;
	margin-bottom: 16px;
	opacity: 0.8;
}

.ccb-vars-empty h5 {
	margin: 0 0 8px 0;
	font-size: 18px;
	font-weight: 700;
}

.ccb-vars-empty p {
	margin: 0 0 20px 0;
	opacity: 0.8;
	font-size: 14px;
}

.ccb-vars-create-btn {
	background: rgba(255,255,255,0.95);
	color: #4f46e5;
	text-decoration: none;
	padding: 12px 24px;
	border-radius: 10px;
	font-weight: 600;
	font-size: 14px;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	display: inline-block;
	backdrop-filter: blur(10px);
}

.ccb-vars-create-btn:hover {
	background: white;
	transform: translateY(-2px);
	box-shadow: 0 8px 24px rgba(0,0,0,0.15);
	text-decoration: none;
	color: #4f46e5;
}

/* AVAILABLE FIELDS STYLING */
.available-fields {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	margin-top: 8px;
}

.available-field {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 6px 12px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.2s ease;
	color: #495057;
}

.available-field:hover {
	background: #e9ecef;
	border-color: #6c757d;
	transform: translateY(-1px);
}

/* HIDE OLD VARIABLES SECTIONS - AGGRESSIVE APPROACH */
/* Hide any elements that might be old variables sections */
*:not(#ccb-variables-system-v2):not(.ccb-vars-panel):not(.ccb-var-card):not(.ccb-vars-header):not(.ccb-vars-title):not(.ccb-vars-actions):not(.ccb-vars-loading):not(.ccb-vars-grid):not(.ccb-vars-empty) {
	/* Will be handled by JavaScript for precise control */
}
</style>

<script>
// VARIABLES SYSTEM V2 - COMPLETELY REWRITTEN FROM SCRATCH
(function() {
	'use strict';

	console.log('🚀 VARIABLES SYSTEM V2 - Starting initialization...');

	// System state
	const VariablesSystem = {
		state: {
			loaded: false,
			variables: [],
			cache: new Map(),
			lastUpdate: 0
		},

		elements: {
			loading: null,
			grid: null,
			empty: null,
			count: null,
			refresh: null
		},

		// Initialize DOM elements
		initElements() {
			this.elements = {
				loading: document.getElementById('ccb-vars-loading'),
				grid: document.getElementById('ccb-vars-grid'),
				empty: document.getElementById('ccb-vars-empty'),
				count: document.getElementById('ccb-vars-count'),
				refresh: document.getElementById('ccb-vars-refresh')
			};

			console.log('📋 Elements initialized:', this.elements);
			return Object.values(this.elements).every(el => el !== null);
		},

		// Load variables with cache and real-time updates
		async loadVariables(forceRefresh = false) {
			console.log('🔄 Loading variables...', { forceRefresh });

			// Check cache first (unless force refresh)
			if (!forceRefresh && this.state.loaded && this.state.variables.length > 0) {
				console.log('📋 Using cached variables');
				this.renderVariables();
				return;
			}

			this.showLoading();

			try {
				// Method 1: Try ajax_window first
				if (window.ajax_window && window.ajax_window.variables) {
					console.log('📡 Loading from ajax_window');
					this.state.variables = window.ajax_window.variables;
					this.state.loaded = true;
					this.renderVariables();
					return;
				}

				// Method 2: Try direct ccb_variables
				if (window.ccb_variables) {
					console.log('📡 Loading from ccb_variables');
					this.state.variables = window.ccb_variables;
					this.state.loaded = true;
					this.renderVariables();
					return;
				}

				// Method 3: AJAX fallback
				await this.loadViaAjax();

			} catch (error) {
				console.error('❌ Error loading variables:', error);
				this.showEmpty();
			}
		},

		// Load via AJAX
		async loadViaAjax() {
			console.log('📡 Loading variables via AJAX...');

			if (!window.ajax_window || !window.ajax_window.ajax_url) {
				throw new Error('AJAX not available');
			}

			const formData = new FormData();
			formData.append('action', 'ccb_get_variables');
			if (window.ajax_window.nonces && window.ajax_window.nonces.ccb_get_variables) {
				formData.append('nonce', window.ajax_window.nonces.ccb_get_variables);
			}

			const response = await fetch(window.ajax_window.ajax_url, {
				method: 'POST',
				body: formData
			});

			const data = await response.json();

			if (data.success && data.data && data.data.variables) {
				this.state.variables = data.data.variables;
				this.state.loaded = true;
				this.state.lastUpdate = Date.now();

				// Update ajax_window for future use
				if (window.ajax_window) {
					window.ajax_window.variables = data.data.variables;
				}

				this.renderVariables();
				console.log('✅ Variables loaded via AJAX:', this.state.variables);
			} else {
				throw new Error('Invalid AJAX response: ' + JSON.stringify(data));
			}
		},

		// Show loading state
		showLoading() {
			if (this.elements.loading) this.elements.loading.style.display = 'flex';
			if (this.elements.grid) this.elements.grid.style.display = 'none';
			if (this.elements.empty) this.elements.empty.style.display = 'none';
		},

		// Show variables grid
		showVariables() {
			if (this.elements.loading) this.elements.loading.style.display = 'none';
			if (this.elements.grid) this.elements.grid.style.display = 'grid';
			if (this.elements.empty) this.elements.empty.style.display = 'none';
		},

		// Show empty state
		showEmpty() {
			if (this.elements.loading) this.elements.loading.style.display = 'none';
			if (this.elements.grid) this.elements.grid.style.display = 'none';
			if (this.elements.empty) this.elements.empty.style.display = 'block';
		},

		// Render variables in grid
		renderVariables() {
			if (!this.elements.grid) {
				console.error('❌ Variables grid element not found');
				return;
			}

			// Clear existing content
			this.elements.grid.innerHTML = '';

			// Update count
			if (this.elements.count) {
				this.elements.count.textContent = this.state.variables.length;
			}

			if (!this.state.variables || this.state.variables.length === 0) {
				this.showEmpty();
				return;
			}

			// Create variable cards
			this.state.variables.forEach(variable => {
				const card = this.createVariableCard(variable);
				this.elements.grid.appendChild(card);
			});

			this.showVariables();
			console.log(`✅ Rendered ${this.state.variables.length} variables successfully!`);
		},

		// Create variable card
		createVariableCard(variable) {
			const card = document.createElement('div');
			card.className = 'ccb-var-card';
			card.innerHTML = `
				<div class="ccb-var-name">${variable.name}</div>
				<div class="ccb-var-value">${variable.value}</div>
				${variable.description ? `<div class="ccb-var-desc">${variable.description}</div>` : ''}
			`;
			card.title = `Click to insert $${variable.name} into formula`;

			card.addEventListener('click', () => {
				this.insertVariable('$' + variable.name);

				// Visual feedback
				card.style.transform = 'scale(0.98)';
				setTimeout(() => {
					card.style.transform = '';
				}, 150);
			});

			return card;
		},

		// Insert variable into formula field (supports both legacy and new formula)
		insertVariable(syntax) {
			console.log('🎯 Inserting variable:', syntax);

			// Find formula input field - try multiple selectors for both legacy and new formula
			const selectors = [
				// Total field formula textarea (primary target)
				'textarea[v-model="totalField.costCalcFormula"]',
				'textarea.calc-textarea',
				'.formula-view textarea',
				// New formula field
				'textarea[v-model*="costCalcFormula"]',
				'textarea[placeholder*="formula"]',
				'input[v-model*="costCalcFormula"]',
				'input[placeholder*="formula"]',
				// Legacy formula field
				'textarea[v-model*="legacyFormula"]',
				'input[v-model*="legacyFormula"]',
				// Generic selectors
				'.formula-field input',
				'input[type="text"].ccb-heading-5',
				'[data-formula-input]',
				'.ccb-formula-input'
			];

			let formulaInput = null;
			for (let selector of selectors) {
				const elements = document.querySelectorAll(selector);
				for (let element of elements) {
					if (element && element.offsetParent !== null) { // visible element
						formulaInput = element;
						console.log('📍 Found formula input:', selector, element);
						break;
					}
				}
				if (formulaInput) break;
			}

			if (formulaInput) {
				const start = formulaInput.selectionStart || 0;
				const end = formulaInput.selectionEnd || 0;
				const currentValue = formulaInput.value || '';

				const newValue = currentValue.substring(0, start) + syntax + currentValue.substring(end);

				// Update the input value
				formulaInput.value = newValue;

				// Trigger multiple events to ensure Vue reactivity
				const events = ['input', 'change', 'keyup'];
				events.forEach(eventType => {
					const event = new Event(eventType, { bubbles: true });
					formulaInput.dispatchEvent(event);
				});

				// Set cursor position after the inserted variable
				setTimeout(() => {
					formulaInput.focus();
					formulaInput.setSelectionRange(start + syntax.length, start + syntax.length);
				}, 10);

				console.log('✅ Variable inserted successfully!');
				this.showNotification('Variable inserted: ' + syntax, 'success');
			} else {
				console.warn('⚠️ Formula input not found, copying to clipboard');
				this.copyToClipboard(syntax);
			}
		},

		// Copy to clipboard fallback
		copyToClipboard(text) {
			if (navigator.clipboard) {
				navigator.clipboard.writeText(text).then(() => {
					this.showNotification('Variable copied to clipboard: ' + text, 'info');
				});
			} else {
				// Fallback for older browsers
				const textArea = document.createElement('textarea');
				textArea.value = text;
				document.body.appendChild(textArea);
				textArea.select();
				document.execCommand('copy');
				document.body.removeChild(textArea);
				this.showNotification('Variable copied to clipboard: ' + text, 'info');
			}
		},

		// Show notification
		showNotification(message, type = 'info') {
			const notification = document.createElement('div');
			notification.style.cssText = `
				position: fixed;
				top: 20px;
				right: 20px;
				background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
				color: white;
				padding: 12px 20px;
				border-radius: 8px;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15);
				z-index: 10000;
				font-size: 14px;
				max-width: 300px;
				animation: slideIn 0.3s ease-out;
			`;
			notification.textContent = message;

			document.body.appendChild(notification);

			// Remove after 3 seconds
			setTimeout(() => {
				notification.style.animation = 'slideIn 0.3s ease-out reverse';
				setTimeout(() => {
					if (notification.parentNode) {
						notification.parentNode.removeChild(notification);
					}
				}, 300);
			}, 3000);
		},

		// Hide old variables sections aggressively
		hideOldVariablesSections() {
			console.log('🔍 Hiding old variables sections...');

			// Find and hide any element containing "Variables" or "Loading variables"
			// that is not part of our new system
			const allElements = document.querySelectorAll('*');
			allElements.forEach(element => {
				if (element && element.textContent) {
					const text = element.textContent.trim();

					if ((text.includes('Variables') || text.includes('Loading variables')) &&
						!element.closest('#ccb-variables-system-v2') &&
						element.id !== 'ccb-variables-system-v2') {

						// Make sure it's not just a small text node
						if (element.children.length > 0 || text.length > 10) {
							element.style.display = 'none';
							console.log('🚫 Hidden old variables section:', element);
						}
					}
				}
			});
		},

		// Initialize the system
		async init() {
			console.log('🎬 Initializing Variables System V2...');

			// Initialize DOM elements
			if (!this.initElements()) {
				console.error('❌ Failed to initialize DOM elements');
				return;
			}

			// Hide old variables sections
			this.hideOldVariablesSections();

			// Set up refresh button
			if (this.elements.refresh) {
				this.elements.refresh.addEventListener('click', () => {
					this.loadVariables(true); // Force refresh
				});
			}

			// Load variables
			await this.loadVariables();

			// Set up periodic refresh to catch variable updates
			setInterval(() => {
				this.loadVariables(true);
			}, 30000); // Refresh every 30 seconds

			console.log('✅ Variables System V2 initialized successfully!');
		}
	};

	// Initialize the system when DOM is ready
	if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', () => {
			VariablesSystem.init();
		});
	} else {
		VariablesSystem.init();
	}

	// Global functions for backward compatibility and debugging
	window.VariablesSystemV2 = VariablesSystem;
	window.refreshVariables = () => VariablesSystem.loadVariables(true);
	window.insertVariable = (syntax) => VariablesSystem.insertVariable(syntax);
	window.hideOldVariablesSections = () => VariablesSystem.hideOldVariablesSections();

	// Debug function for testing
	window.testVariablesSystemV2 = function() {
		console.log('🧪 Testing Variables System V2...');
		console.log('System state:', VariablesSystem.state);
		console.log('Elements:', VariablesSystem.elements);
		console.log('Window variables:', window.ccb_variables);
		console.log('Ajax window variables:', window.ajax_window?.variables);

		// Force refresh
		VariablesSystem.loadVariables(true);
	};

})();
</script>

<script>
// Vue.js Component Definition for Total Field
Vue.component('total-field', {
	props: ['field', 'id', 'index', 'order', 'available'],
	data() {
		return {
			tab: 'main',
			totalField: {
				label: 'Total',
				description: '',
				costCalcFormula: '',
				addToSummary: true,
				hidden: false,
				additionalStyles: '',
				...this.field
			},
			errors: {},
			errorsCount: 0
		};
	},
	computed: {
		available_fields() {
			// Get available fields from the builder
			const fields = [];

			if (this.available && Array.isArray(this.available)) {
				this.available.forEach(field => {
					if (field.alias) {
						fields.push({
							alias: field.alias,
							label: field.label || field.alias
						});
					}
				});
			}

			// Add variables if available
			if (window.ajax_window && window.ajax_window.variables && Array.isArray(window.ajax_window.variables)) {
				window.ajax_window.variables.forEach(variable => {
					fields.push({
						alias: '$' + variable.name,
						label: '$' + variable.name + ' (' + variable.value + ')'
					});
				});
			}

			return fields;
		}
	},
	methods: {
		insertField(alias) {
			// Insert field alias into formula textarea
			const textarea = this.$el.querySelector('textarea[v-model="totalField.costCalcFormula"]') ||
							this.$el.querySelector('textarea.calc-textarea');

			if (textarea) {
				const start = textarea.selectionStart || 0;
				const end = textarea.selectionEnd || 0;
				const currentValue = textarea.value || '';

				const newValue = currentValue.substring(0, start) + alias + currentValue.substring(end);

				// Update the model
				this.totalField.costCalcFormula = newValue;

				// Set cursor position after the inserted field
				this.$nextTick(() => {
					textarea.focus();
					textarea.setSelectionRange(start + alias.length, start + alias.length);
				});
			}
		},

		save(field, id, index, alias) {
			// Emit save event
			this.$emit('save', {
				field: this.totalField,
				id: id,
				index: index,
				alias: alias
			});
		},

		isObjectHasPath(obj, path) {
			// Helper method to check if object has nested path
			return path.reduce((current, key) => {
				return current && current[key] !== undefined;
			}, obj);
		}
	},
	watch: {
		field: {
			handler(newField) {
				if (newField) {
					this.totalField = {
						label: 'Total',
						description: '',
						costCalcFormula: '',
						addToSummary: true,
						hidden: false,
						additionalStyles: '',
						...newField
					};
				}
			},
			immediate: true,
			deep: true
		}
	}
});
</script>
