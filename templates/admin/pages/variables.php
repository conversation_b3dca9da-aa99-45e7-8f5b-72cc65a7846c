<?php
// Ensure table exists with correct name
\cBuilder\Classes\Database\Variables::create_table();

// Get existing variables
$variables = \cBuilder\Classes\Database\Variables::get_all_variables();

// Enqueue styles and scripts
wp_enqueue_style( 'ccb-bootstrap-css', CALC_URL . '/frontend/dist/css/bootstrap.min.css', array(), CALC_VERSION );
wp_enqueue_style( 'ccb-admin-app-css', CALC_URL . '/frontend/dist/css/admin.css', array(), CALC_VERSION );
wp_enqueue_script( 'jquery' );
wp_enqueue_script( 'ccb-variables-js', CALC_URL . '/frontend/dist/variables.js', array( 'jquery' ), CALC_VERSION, true );
wp_localize_script(
	'ccb-variables-js',
	'ccb_variables_data',
	array(
		'ajax_url'      => admin_url( 'admin-ajax.php' ),
		'nonces'        => array(
			'ccb_get_variables'          => wp_create_nonce( 'ccb_get_variables' ),
			'ccb_save_variable'          => wp_create_nonce( 'ccb_save_variable' ),
			'ccb_delete_variable'        => wp_create_nonce( 'ccb_delete_variable' ),
			'ccb_get_variables_for_formula' => wp_create_nonce( 'ccb_get_variables_for_formula' ),
		),
		'variables'     => $variables,
		'translations'  => array(
			'add_variable'       => __( 'Add Variable', 'cost-calculator-builder' ),
			'edit_variable'      => __( 'Edit Variable', 'cost-calculator-builder' ),
			'delete_variable'    => __( 'Delete Variable', 'cost-calculator-builder' ),
			'variable_name'      => __( 'Variable Name', 'cost-calculator-builder' ),
			'variable_value'     => __( 'Value', 'cost-calculator-builder' ),
			'variable_description' => __( 'Description', 'cost-calculator-builder' ),
			'save'               => __( 'Save', 'cost-calculator-builder' ),
			'cancel'             => __( 'Cancel', 'cost-calculator-builder' ),
			'delete'             => __( 'Delete', 'cost-calculator-builder' ),
			'confirm_delete'     => __( 'Are you sure you want to delete this variable?', 'cost-calculator-builder' ),
		),
	)
);
?>

<div class="wrap ccb-variables-page" id="ccb_variables_page">
	<h1 class="wp-heading-inline"><?php esc_html_e( 'Variables', 'cost-calculator-builder' ); ?></h1>
	<a href="#" class="page-title-action" id="ccb-add-variable-btn"><?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?></a>
	<a href="#" class="page-title-action" id="ccb-debug-table-btn" style="background: #dc3232;"><?php esc_html_e( 'Debug Table', 'cost-calculator-builder' ); ?></a>
	<hr class="wp-header-end">

	<p class="description">
		<?php esc_html_e( 'Create and manage global variables that can be used in calculator formulas. Variables are referenced using the syntax $variable_name in formulas.', 'cost-calculator-builder' ); ?>
	</p>

	<div id="ccb-variables-content">
		<div id="ccb-variables-loader" style="display: none;">
			<p><?php esc_html_e( 'Loading...', 'cost-calculator-builder' ); ?></p>
		</div>

		<div id="ccb-variables-table-container">
			<?php if ( empty( $variables ) ) : ?>
				<div class="ccb-empty-state">
					<h3><?php esc_html_e( 'No Variables Found', 'cost-calculator-builder' ); ?></h3>
					<p><?php esc_html_e( 'Create your first variable to use in calculator formulas.', 'cost-calculator-builder' ); ?></p>
					<button type="button" class="button button-primary" id="ccb-add-first-variable-btn">
						<?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?>
					</button>
				</div>
			<?php else : ?>
				<table class="wp-list-table widefat fixed striped" id="ccb-variables-table">
					<thead>
						<tr>
							<th scope="col"><?php esc_html_e( 'Name', 'cost-calculator-builder' ); ?></th>
							<th scope="col"><?php esc_html_e( 'Value', 'cost-calculator-builder' ); ?></th>
							<th scope="col"><?php esc_html_e( 'Description', 'cost-calculator-builder' ); ?></th>
							<th scope="col"><?php esc_html_e( 'Syntax', 'cost-calculator-builder' ); ?></th>
							<th scope="col"><?php esc_html_e( 'Actions', 'cost-calculator-builder' ); ?></th>
						</tr>
					</thead>
					<tbody id="ccb-variables-tbody">
						<?php foreach ( $variables as $variable ) : ?>
							<tr data-variable-id="<?php echo esc_attr( $variable['id'] ); ?>">
								<td><strong><?php echo esc_html( $variable['name'] ); ?></strong></td>
								<td><?php echo esc_html( $variable['value'] ); ?></td>
								<td><?php echo esc_html( $variable['description'] ?: '-' ); ?></td>
								<td><code>$<?php echo esc_html( $variable['name'] ); ?></code></td>
								<td>
									<button type="button" class="button button-small ccb-edit-variable" data-variable-id="<?php echo esc_attr( $variable['id'] ); ?>">
										<?php esc_html_e( 'Edit', 'cost-calculator-builder' ); ?>
									</button>
									<button type="button" class="button button-small button-link-delete ccb-delete-variable" data-variable-id="<?php echo esc_attr( $variable['id'] ); ?>">
										<?php esc_html_e( 'Delete', 'cost-calculator-builder' ); ?>
									</button>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			<?php endif; ?>
		</div>
	</div>

	<!-- Add/Edit Variable Modal -->
	<div id="ccb-variable-modal" class="ccb-modal" style="display: none;">
		<div class="ccb-modal-content">
			<div class="ccb-modal-header">
				<h2 id="ccb-modal-title"><?php esc_html_e( 'Add Variable', 'cost-calculator-builder' ); ?></h2>
				<span class="ccb-modal-close">&times;</span>
			</div>
			<div class="ccb-modal-body">
				<form id="ccb-variable-form">
					<input type="hidden" id="ccb-variable-id" value="">

					<table class="form-table">
						<tr>
							<th scope="row">
								<label for="ccb-variable-name"><?php esc_html_e( 'Variable Name', 'cost-calculator-builder' ); ?> *</label>
							</th>
							<td>
								<input type="text" id="ccb-variable-name" name="variable_name" class="regular-text" required>
								<p class="description">
									<?php esc_html_e( 'Use only letters, numbers, and underscores. Must start with a letter or underscore.', 'cost-calculator-builder' ); ?>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row">
								<label for="ccb-variable-value"><?php esc_html_e( 'Value', 'cost-calculator-builder' ); ?> *</label>
							</th>
							<td>
								<input type="number" step="0.01" id="ccb-variable-value" name="variable_value" class="regular-text" required>
								<p class="description">
									<?php esc_html_e( 'Enter numeric value for this variable.', 'cost-calculator-builder' ); ?>
								</p>
							</td>
						</tr>
						<tr>
							<th scope="row">
								<label for="ccb-variable-description"><?php esc_html_e( 'Description', 'cost-calculator-builder' ); ?></label>
							</th>
							<td>
								<textarea id="ccb-variable-description" name="variable_description" class="large-text" rows="3"></textarea>
								<p class="description">
									<?php esc_html_e( 'Optional description for this variable.', 'cost-calculator-builder' ); ?>
								</p>
							</td>
						</tr>
						<tr id="ccb-variable-preview-row" style="display: none;">
							<th scope="row">
								<?php esc_html_e( 'Formula Syntax', 'cost-calculator-builder' ); ?>
							</th>
							<td>
								<code id="ccb-variable-preview">$variable_name</code>
								<p class="description">
									<?php esc_html_e( 'Use this syntax in your calculator formulas.', 'cost-calculator-builder' ); ?>
								</p>
							</td>
						</tr>
					</table>
				</form>
			</div>
			<div class="ccb-modal-footer">
				<button type="button" class="button" id="ccb-modal-cancel"><?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?></button>
				<button type="button" class="button button-primary" id="ccb-modal-save"><?php esc_html_e( 'Save', 'cost-calculator-builder' ); ?></button>
			</div>
		</div>
	</div>

	<!-- Delete Confirmation Modal -->
	<div id="ccb-delete-modal" class="ccb-modal" style="display: none;">
		<div class="ccb-modal-content">
			<div class="ccb-modal-header">
				<h2><?php esc_html_e( 'Delete Variable', 'cost-calculator-builder' ); ?></h2>
				<span class="ccb-modal-close">&times;</span>
			</div>
			<div class="ccb-modal-body">
				<p><?php esc_html_e( 'Are you sure you want to delete this variable? This action cannot be undone and may affect calculators that use this variable.', 'cost-calculator-builder' ); ?></p>
			</div>
			<div class="ccb-modal-footer">
				<button type="button" class="button" id="ccb-delete-cancel"><?php esc_html_e( 'Cancel', 'cost-calculator-builder' ); ?></button>
				<button type="button" class="button button-primary" id="ccb-delete-confirm"><?php esc_html_e( 'Delete', 'cost-calculator-builder' ); ?></button>
			</div>
		</div>
	</div>
</div>

<style>
/* Modal Styles */
.ccb-modal {
	position: fixed;
	z-index: 100000;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0,0,0,0.5);
}

.ccb-modal-content {
	background-color: #fefefe;
	margin: 5% auto;
	border: 1px solid #888;
	border-radius: 4px;
	width: 80%;
	max-width: 600px;
	box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.ccb-modal-header {
	padding: 20px;
	border-bottom: 1px solid #eee;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.ccb-modal-header h2 {
	margin: 0;
	font-size: 18px;
}

.ccb-modal-close {
	color: #aaa;
	font-size: 28px;
	font-weight: bold;
	cursor: pointer;
	line-height: 1;
}

.ccb-modal-close:hover,
.ccb-modal-close:focus {
	color: #000;
	text-decoration: none;
}

.ccb-modal-body {
	padding: 20px;
}

.ccb-modal-footer {
	padding: 20px;
	border-top: 1px solid #eee;
	text-align: right;
}

.ccb-modal-footer .button {
	margin-left: 10px;
}

/* Empty State */
.ccb-empty-state {
	text-align: center;
	padding: 40px 20px;
	background: #fff;
	border: 1px solid #ccd0d4;
	border-radius: 4px;
}

.ccb-empty-state h3 {
	margin-top: 0;
	color: #555;
}

.ccb-empty-state p {
	color: #666;
	margin-bottom: 20px;
}

/* Variable Preview */
#ccb-variable-preview {
	background: #f1f1f1;
	padding: 8px 12px;
	border-radius: 3px;
	font-family: Consolas, Monaco, monospace;
	color: #d63384;
	border: 1px solid #ddd;
}

/* Responsive */
@media (max-width: 768px) {
	.ccb-modal-content {
		width: 95%;
		margin: 10% auto;
	}

	.ccb-modal-header,
	.ccb-modal-body,
	.ccb-modal-footer {
		padding: 15px;
	}
}
</style>
