<?php
$is_settings = isset( $_GET['tab'] ) && sanitize_text_field( $_GET['tab'] ) === 'settings'; // phpcs:ignore WordPress.Security.NonceVerification

// Load variables for injection - Robust approach
$ccb_variables = array();

// Try database first
global $wpdb;
$table_name = $wpdb->prefix . 'cc_variables';

try {
    $db_variables = $wpdb->get_results("SELECT * FROM {$table_name}", ARRAY_A);

    if ($db_variables && count($db_variables) > 0) {
        foreach ($db_variables as $var) {
            $ccb_variables[] = array(
                'id' => intval($var['id']),
                'name' => sanitize_text_field($var['name']),
                'value' => floatval($var['value']),
                'description' => isset($var['description']) ? sanitize_text_field($var['description']) : ''
            );
        }
    }
} catch (Exception $e) {
    error_log('CCB Variables Database Error: ' . $e->getMessage());
}

// Fallback: If no database variables, use hardcoded test data
if (empty($ccb_variables)) {
    $ccb_variables = array(
        array(
            'id' => 1,
            'name' => 'paper',
            'value' => 105000.00,
            'description' => 'Paper cost variable (fallback)'
        ),
        array(
            'id' => 2,
            'name' => 'labor',
            'value' => 50.00,
            'description' => 'Labor cost per hour (fallback)'
        )
    );
}

// Log variables count for debugging
error_log('CCB Variables loaded: ' . count($ccb_variables) . ' variables');
wp_enqueue_script( 'cbb-bundle-js', CALC_URL . '/frontend/dist/admin.js', array(), CALC_VERSION, true );
wp_localize_script(
	'cbb-bundle-js',
	'ajax_window',
	array(
		'preview_link'      => get_admin_url( null, 'admin.php?page=cost_calculator_preview&preview_calc_id=' ),
		'ajax_url'          => admin_url( 'admin-ajax.php' ),
		'condition_actions' => \cBuilder\Helpers\CCBConditionsHelper::getActions(),
		'condition_states'  => \cBuilder\Helpers\CCBConditionsHelper::getConditionStates(),
		'dateFormat'        => get_option( 'date_format' ),
		'language'          => substr( get_bloginfo( 'language' ), 0, 2 ),
		'plugin_url'        => CALC_URL,
		'site_url'          => site_url(),
		'templates'         => \cBuilder\Helpers\CCBFieldsHelper::get_fields_templates(),
		'order_templates'   => \cBuilder\Helpers\CCBFieldsHelper::get_order_fields_templates(),
		'translations'      => array_merge( \cBuilder\Classes\CCBTranslations::get_frontend_translations(), \cBuilder\Classes\CCBTranslations::get_backend_translations() ),
		'pro_active'        => ccb_pro_active(),
		'edit_pencil'       => CALC_URL . '/frontend/dist/img/edit_pencil.svg',
		'variables'         => $ccb_variables,
		'nonces'            => array(
			'ccb_get_variables'          => wp_create_nonce( 'ccb_get_variables' ),
			'ccb_save_variable'          => wp_create_nonce( 'ccb_save_variable' ),
			'ccb_delete_variable'        => wp_create_nonce( 'ccb_delete_variable' ),
			'ccb_get_variables_for_formula' => wp_create_nonce( 'ccb_get_variables_for_formula' ),
		),
	)
);
?>

<!-- Variables Injection -->
<script>
// Ensure variables are available for the Beautiful Variables System
window.ccb_variables = <?php echo json_encode($ccb_variables); ?>;

console.log('✅ Variables loaded:', window.ccb_variables.length, 'variables');

// Ensure ajax_window has variables (backup for wp_localize_script)
document.addEventListener('DOMContentLoaded', function() {
    if (typeof window.ajax_window !== 'undefined' && !window.ajax_window.variables) {
        window.ajax_window.variables = window.ccb_variables;
        console.log('✅ Injected variables into ajax_window');
    }
});
</script>

<?php require_once CALC_PATH . '/templates/admin/components/notice-mobile.php'; ?>

<div class="ccb-settings-wrapper calculator-settings" id="cost_calculator_main_page">
	<calc-builder inline-template>
		<div class="ccb-main-container">
			<template v-if="!$store.getters.getHideHeader">
				<?php require_once CALC_PATH . '/templates/admin/components/header.php'; ?>
			</template>
			<div class="ccb-tab-content">
				<div class="ccb-tab-sections ccb-loader-section" v-if="loader">
					<loader></loader>
				</div>
				<template v-else>
					<?php if ( $is_settings ) : ?>
						<general-settings inline-template>
							<?php require_once CALC_PATH . '/templates/admin/pages/settings.php'; ?>
						</general-settings>
					<?php else : ?>
						<div class="ccb-field-overlay" v-if="$store.getters.getType?.length !== 0"></div>
						<calculators-page inline-template>
							<?php require_once CALC_PATH . '/templates/admin/pages/calculator.php'; ?>
						</calculators-page>
					<?php endif; ?>
				</template>
			</div>
		</div>
	</calc-builder>
</div>
