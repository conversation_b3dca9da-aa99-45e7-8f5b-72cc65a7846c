{"calculators": [{"ccb_name": "Sample Calculator", "ccb_fields": [{"unit": "1", "label": "Height", "_id": "0", "default": "5", "description": "[centimeters]", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_0", "text": "Quantity Field", "required": false, "hidden": null}, {"unit": "1", "label": "<PERSON><PERSON><PERSON>", "_id": "1", "default": "40", "description": "[centimeters]", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_1", "text": "Quantity Field", "required": false, "hidden": null}, {"unit": "1", "label": "De<PERSON><PERSON>", "_id": "2", "default": "20", "description": "centimeters", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_2", "text": "Quantity Field", "required": false, "hidden": null}, {"step": "1", "unit": "1", "sign": "miles", "label": "Distance", "default": "15", "_id": "3", "minValue": "0", "maxValue": "50", "description": "[miles]", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_3", "text": "Range slider", "required": false, "hidden": null}, {"label": "Type of Service", "_id": 6, "default": "", "onValue": null, "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "addToSummary": true, "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_6", "desc_option": "after", "summary_view": "show_value", "styles": {"box_style": "vertical", "style": "default"}, "options": [{"optionText": "Within 2 working days", "optionValue": "30"}, {"optionText": "Door to door", "optionValue": "50"}, {"optionText": "Same day", "optionValue": "100"}], "apply_style_for_all": false, "text": "Radio fields", "hidden": null}, {"_id": 5, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": "if( ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3) { ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3 + radio_field_id_6 }", "additionalStyles": "", "alias": "total_field_id_5", "icon": "ccb-icon-Path-3516", "label": "Total description", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Total fields"}], "ccb_formula": [{"id": 5, "alias": "total_field_id_5", "label": "Total description", "hidden": null, "formula": "if( ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3) { ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3 + radio_field_id_6 }", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": ""}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Total Summary", "descriptions": true, "hide_empty": true, "sticky": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}, "show_details_accordion": true}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "submitBtnText": "Submit", "allowContactForm": "", "body": "Dear sir/madam<PERSON><PERSON> would be very grateful to you if you could provide us the quotation of the following=>Total Summary[ccb-subtotal]Total=> [ccb-total-0]Looking forward to hearing back from you.Thanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": true, "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 220, "webhooks": {"enableSendForms": false, "enableEmailQuote": false, "enablePaymentBtn": false}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}], "categories": []}