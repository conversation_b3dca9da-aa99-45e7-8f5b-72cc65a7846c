{"calculators": [{"ccb_name": "<PERSON><PERSON><PERSON>", "ccb_fields": [{"label": "Which Hall do you want to book?", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "Conference Center", "optionValue": "1"}, {"optionText": "Community Grand Hall", "optionValue": "2"}, {"optionText": "Art Gallery", "optionValue": "3"}, {"optionText": "Academic venue", "optionValue": "4"}], "hidden": null, "text": "Dropdown list"}, {"step": 1, "unit": 1, "sign": "", "label": "How many guests are you expecting?", "_id": 1, "default": "", "minValue": "0", "maxValue": "1000", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_1", "desc_option": "after", "required": false, "hidden": null, "text": "Basic slider"}, {"unit": "15", "label": "Food Option (Per Person)", "_id": 2, "default": "", "description": "", "placeholder": "", "required": false, "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "enabled_currency_settings": false, "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_2", "desc_option": "after", "step": 1, "hidden": null, "text": "Quantity field"}, {"label": "Event Timing", "_id": 3, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_3", "desc_option": "after", "options": [{"optionText": "11.00 a.m - 5.00 p.m", "optionValue": "1"}, {"optionText": "6.00 p.m - 12.00 a.m", "optionValue": "2"}], "hidden": null, "text": "Dropdown list"}, {"_id": 4, "label": "Extra Servings", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_4", "desc_option": "after", "options": [{"optionText": "Microphone", "optionValue": "1", "optionHint": ""}, {"optionText": "Stage", "optionValue": "2", "optionHint": ""}, {"optionText": "Flower Decoration", "optionValue": "3", "optionHint": ""}], "horizontally_view": true, "hidden": null, "text": "Checkbox"}, {"_id": 5, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " dropDown_field_id_0 + ( range_field_id_1 * quantity_field_id_2 ) + dropDown_field_id_3 + checkbox_field_id_4 ", "additionalStyles": "", "alias": "total_field_id_5", "icon": "ccb-icon-Path-3516", "label": "Total", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 5, "alias": "total_field_id_5", "label": "Total", "hidden": null, "formula": " dropDown_field_id_0 + ( range_field_id_1 * quantity_field_id_2 ) + dropDown_field_id_3 + checkbox_field_id_4 ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_5", "total": 0, "converted": "$ 0.00", "label": "Total", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings"}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "<PERSON><PERSON>es", "ccb_fields": [{"label": "Which Subject Major Would You Like to Study", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "Accounting & Finance", "optionValue": "8000"}, {"optionText": "Architecture Engineering", "optionValue": "16000"}, {"optionText": "Biomedical Engineering", "optionValue": "16000"}, {"optionText": "Marketing & Management", "optionValue": "9000"}], "hidden": null, "text": "Dropdown list"}, {"label": "Books & Supply Cost", "_id": 1, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_1", "desc_option": "after", "options": [{"optionText": "First Year", "optionValue": "2100"}, {"optionText": "Second Year", "optionValue": "2200"}, {"optionText": "Third Year", "optionValue": "2500"}, {"optionText": "Final Year", "optionValue": "2900"}], "hidden": null, "text": "Dropdown list"}, {"label": "Residence", "_id": 2, "default": "", "onValue": null, "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "allowCurrency": "", "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_2", "desc_option": "after", "options": [{"optionText": "In Campus", "optionValue": "1500"}, {"optionText": "Off Campus", "optionValue": "0"}], "hidden": null, "text": "Radio select"}, {"label": "Choose your meal plan", "_id": 3, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_3", "desc_option": "after", "options": [{"optionText": "Veg", "optionValue": "300"}, {"optionText": "Non Veg", "optionValue": "450"}, {"optionText": "I'll have my meals outside of campus", "optionValue": "0"}], "hidden": null, "text": "Dropdown list"}, {"_id": 4, "label": "Do you have a scholarship?", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_4", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "1", "optionHint": ""}], "hidden": null, "text": "Switch toggle"}, {"label": "How much Scholarship are you getting?", "_id": 5, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_5", "desc_option": "after", "options": [{"optionText": "International Scholarship", "optionValue": "-3500"}, {"optionText": "Government Scholarship", "optionValue": "-3000"}], "hidden": null, "text": "Dropdown list"}, {"_id": 6, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": "( dropDown_field_id_0 + dropDown_field_id_1 + radio_field_id_2 + dropDown_field_id_3 ) + (toggle_field_id_4 * dropDown_field_id_5 )", "additionalStyles": "", "alias": "total_field_id_6", "icon": "ccb-icon-Path-3516", "label": "Total Summary", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 6, "alias": "total_field_id_6", "label": "Total Summary", "hidden": null, "formula": "( dropDown_field_id_0 + dropDown_field_id_1 + radio_field_id_2 + dropDown_field_id_3 ) + (toggle_field_id_4 * dropDown_field_id_5 )", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_6", "total": 0, "converted": "$ 0.00", "label": "Total Summary", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 13}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Renovation", "ccb_fields": [{"step": "20", "unit": "75", "sign": "$", "label": "Square Feet ($75)", "default": "50", "_id": "0", "minValue": "0", "maxValue": "2000", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "1", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_0", "text": "Basic slider", "required": false, "hidden": null}, {"step": "1", "unit": "25", "sign": "$", "label": "Number of Rooms ($25/room)", "default": "3", "_id": "1", "minValue": "1", "maxValue": "10", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "1", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_1", "text": "Basic slider", "required": false, "hidden": null}, {"step": "1", "unit": "80", "sign": "$", "label": "Number of Levels ($80/level)", "default": "5", "_id": "2", "minValue": "1", "maxValue": "30", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "1", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_2", "text": "Basic slider", "required": false, "hidden": null}, {"label": "", "_id": "4", "_event": "", "type": "Html", "htmlContent": "BATHROOM", "placeholder": "", "hidden": null, "_tag": "cost-html", "additionalCss": "", "additionalStyles": "", "icon": "ccb-icon-Path-3517", "alias": "html_field_id_", "text": "Html element", "required": false}, {"label": "Bathroom Design", "_id": "5", "default": "250_0", "description": "", "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "1", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_5", "options": [{"optionText": "Low ($250)", "optionValue": "250"}, {"optionValue": "500", "optionText": "Middle ($500)"}, {"optionValue": "750", "optionText": "High ($750)"}], "text": "Dropdown list", "required": false, "hidden": null}, {"step": "10", "unit": "2", "sign": "$", "label": "Tiles ($2/tile)", "default": "4", "_id": "6", "minValue": "0", "maxValue": "100", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "1", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_6", "text": "Basic slider", "required": false, "hidden": null}, {"step": "10", "unit": "10", "sign": "$", "label": "Pipes to Replace ($10/pipe)", "default": "50", "_id": "7", "minValue": "0", "maxValue": "100", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "1", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_7", "text": "Basic slider", "required": false, "hidden": null}, {"_id": "9", "_event": "", "type": "Html", "htmlContent": "OUTSIDE WORKS", "placeholder": "", "_tag": "cost-html", "additionalStyles": "", "icon": "ccb-icon-Path-3517", "text": "Html element", "required": false, "hidden": null}, {"_id": "10", "label": "Choose options", "description": "", "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "1", "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_10", "options": [{"optionText": "Swimming Pool", "optionValue": "250"}, {"optionText": "Exterior", "optionValue": "200"}], "text": "Checkbox", "required": false, "hidden": null}, {"_id": "11", "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": "( range_field_id_0 + range_field_id_1 + range_field_id_2 ) + ( dropDown_field_id_5 + range_field_id_6 + range_field_id_7 ) + checkbox_field_id_10", "additionalStyles": "", "alias": "total_field_id_11", "icon": "ccb-icon-Path-3516", "label": "Total", "symbol": "$", "default": "", "text": "Formula"}], "ccb_formula": [{"id": "11", "alias": "total_field_id_11", "label": "Total", "hidden": null, "formula": "( range_field_id_0 + range_field_id_1 + range_field_id_2 ) + ( dropDown_field_id_5 + range_field_id_6 + range_field_id_7 ) + checkbox_field_id_10", "additionalStyles": "", "data": {"alias": "total_field_id_11", "total": 362, "converted": "$ 362.00", "label": "Total", "hidden": null, "additionalStyles": ""}, "total": 362}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}, "show_details_accordion": true}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": true, "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam<PERSON><PERSON> would be very grateful to you if you could provide us the quotation of the following=>Total Summary[ccb-subtotal]Total=> [ccb-total-0]Looking forward to hearing back from you.Thanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Renovation", "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 12}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Printing Services", "ccb_fields": [{"label": "Type of Printing", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "Flyer/Leaflet", "optionValue": "10"}, {"optionText": "Poster Printing", "optionValue": "20"}, {"optionText": "Book Printing", "optionValue": "50"}], "hidden": null, "text": "Dropdown list"}, {"label": "Paper Size", "_id": 1, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_1", "desc_option": "after", "options": [{"optionText": "A4", "optionValue": "5"}, {"optionText": "A5", "optionValue": "10"}], "hidden": null, "text": "Dropdown list"}, {"label": "Paper Weight", "_id": 2, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_2", "desc_option": "after", "options": [{"optionText": "60 GSM", "optionValue": "10"}, {"optionText": "80 GSM", "optionValue": "20"}, {"optionText": "100 GSM", "optionValue": "30"}], "hidden": null, "text": "Dropdown list"}, {"step": 1, "unit": 1, "sign": "", "label": "Quantity", "_id": 3, "default": "", "minValue": "0", "maxValue": 100, "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_3", "desc_option": "after", "required": false, "hidden": null, "text": "Basic slider"}, {"_id": 4, "label": "Colors", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_4", "desc_option": "after", "options": [{"optionText": "Black & White", "optionValue": "5", "optionHint": ""}, {"optionText": "Red", "optionValue": "10", "optionHint": ""}, {"optionText": "Blue", "optionValue": "10", "optionHint": ""}, {"optionText": "Yellow", "optionValue": "10", "optionHint": ""}], "hidden": null, "text": "Checkbox"}, {"label": "Lamination", "_id": 5, "default": "", "onValue": null, "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_5", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "15"}, {"optionText": "No", "optionValue": "0"}], "hidden": null, "text": "Radio select"}, {"_id": 6, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " dropDown_field_id_0 + dropDown_field_id_1 + dropDown_field_id_2 + range_field_id_3 + checkbox_field_id_4 + radio_field_id_5 ", "additionalStyles": "", "alias": "total_field_id_6", "icon": "ccb-icon-Path-3516", "label": "Total description", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 6, "alias": "total_field_id_6", "label": "Total description", "hidden": null, "formula": " dropDown_field_id_0 + dropDown_field_id_1 + dropDown_field_id_2 + range_field_id_3 + checkbox_field_id_4 + radio_field_id_5 ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_6", "total": 0, "converted": "$ 0.00", "label": "Total description", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}, "show_details_accordion": true}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 11}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Medical Services", "ccb_fields": [{"label": "What Doctor Are You Seeking?", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "Cardiology", "optionValue": "1"}, {"optionText": "Dermatology", "optionValue": "1"}, {"optionText": "Kidney Specialist", "optionValue": "1"}, {"optionText": "Orthopedic", "optionValue": "1"}], "hidden": null, "text": "Dropdown list"}, {"label": "What Service Are You Seeking?", "_id": 1, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_1", "desc_option": "after", "options": [{"optionText": "Visiting", "optionValue": "800"}, {"optionText": "Follow Up ", "optionValue": "200"}], "hidden": null, "text": "Dropdown list"}, {"_id": 3, "label": "Medical Test", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_3", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "1", "optionHint": ""}], "hidden": null, "text": "Switch toggle"}, {"_id": 2, "label": "Medical Tests", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_2", "desc_option": "after", "options": [{"optionText": "Blood Test", "optionValue": "40", "optionHint": ""}, {"optionText": "X-Ray", "optionValue": "70", "optionHint": ""}, {"optionText": "MRI", "optionValue": "200", "optionHint": ""}], "hidden": null, "text": "Checkbox"}, {"_id": 4, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " ( dropDown_field_id_0 * dropDown_field_id_1 ) + ( toggle_field_id_3 * checkbox_field_id_2 ) ", "additionalStyles": "", "alias": "total_field_id_4", "icon": "ccb-icon-Path-3516", "label": "Total Amount", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 4, "alias": "total_field_id_4", "label": "Total Amount", "hidden": null, "formula": " ( dropDown_field_id_0 * dropDown_field_id_1 ) + ( toggle_field_id_3 * checkbox_field_id_2 ) ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_4", "total": 0, "converted": "$ 0.00", "label": "Total Amount", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings"}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Loan <PERSON>", "ccb_fields": [{"unit": "1", "label": "<PERSON><PERSON>", "_id": "0", "default": "10000", "description": "[Enter amount]", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "1", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_0", "text": "Quantity field", "required": false, "hidden": null}, {"step": "1", "unit": "1", "sign": "", "label": "Annual Interest Rate", "default": "5", "_id": "1", "minValue": "0", "maxValue": "15", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_1", "text": "Basic slider", "required": false, "hidden": null}, {"step": "1", "unit": "1", "sign": "", "label": "Months", "default": "25", "_id": "2", "minValue": "4", "maxValue": "160", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_2", "text": "Basic slider", "required": false, "hidden": null}, {"_id": "3", "symbol": "$", "type": "Total", "_tag": "cost-total", "costCalcFormula": "( range_field_id_1 / 1200 + ( range_field_id_1 /1200 ) / ( Math.pow( 1 + range_field_id_1 /1200, range_field_id_2 ) - 1)) * quantity_field_id_0", "additionalStyles": "", "icon": "ccb-icon-Path-3516", "label": "Total", "text": "Formula"}], "ccb_formula": [{"id": "3", "label": "Total", "hidden": false, "formula": "( range_field_id_1 / 1200 + ( range_field_id_1 /1200 ) / ( Math.pow( 1 + range_field_id_1 /1200, range_field_id_2 ) - 1)) * quantity_field_id_0", "additionalStyles": "", "data": {"total": 422.02696256017595, "converted": "$ 422.03", "label": "Total", "hidden": false, "additionalStyles": ""}, "total": 422.02696256017595}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam<PERSON><PERSON> would be very grateful to you if you could provide us the quotation of the following=>Total Summary[ccb-subtotal]Total=> [ccb-total-0]Looking forward to hearing back from you.Thanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 9}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Graphic Designing", "ccb_fields": [{"label": "Type of Website", "_id": 0, "default": "", "description": "What kind of website do you have?", "required": true, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "eCommerce", "optionValue": "800"}, {"optionText": "Institutional", "optionValue": "650"}, {"optionText": "Corporate", "optionValue": "1000"}], "hidden": null, "text": "Dropdown list"}, {"step": 1, "unit": "50", "sign": "$", "label": "Number of Page Designs", "_id": 1, "default": "", "minValue": "0", "maxValue": 100, "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": true, "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_1", "desc_option": "after", "required": false, "hidden": null, "text": "Basic slider"}, {"label": "Mobile Ready", "_id": 2, "default": "", "onValue": null, "description": "", "required": true, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_2", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "100"}, {"optionText": "No", "optionValue": "0"}], "hidden": null, "text": "Radio select"}, {"label": "HTML (3)", "_id": 3, "_event": "", "type": "Html", "htmlContent": "Other Services", "placeholder": "", "_tag": "cost-html", "additionalCss": "", "additionalStyles": "", "icon": "ccb-icon-Path-3517", "alias": "html_field_id_3", "required": false, "hidden": null, "text": "Html element"}, {"_id": 4, "label": "Addons With The Package", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_4", "desc_option": "after", "options": [{"optionText": "Logo Design", "optionValue": "170", "optionHint": ""}, {"optionText": "Promotion Banner Design", "optionValue": "250", "optionHint": ""}, {"optionText": "Image Slider", "optionValue": "200", "optionHint": ""}, {"optionText": "Seasonal Theme Design", "optionValue": "350", "optionHint": ""}], "horizontally_view": true, "hidden": null, "text": "Checkbox"}, {"_id": 5, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " dropDown_field_id_0 + range_field_id_1 + radio_field_id_2 + checkbox_field_id_4 ", "additionalStyles": "", "alias": "total_field_id_5", "icon": "ccb-icon-Path-3516", "label": "Total Amount", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 5, "alias": "total_field_id_5", "label": "Total Amount", "hidden": null, "formula": " dropDown_field_id_0 + range_field_id_1 + radio_field_id_2 + checkbox_field_id_4 ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_5", "total": 0, "converted": "$ 0.00", "label": "Total Amount", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\r\nWe would be very grateful to you if you could provide us the quotation of the following=>\r\n\r\nTotal Summary\r\n[ccb-subtotal]\r\nTotal: [ccb-total-0]\r\nLooking forward to hearing back from you.\r\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total Amount"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 8}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Dentist", "ccb_fields": [{"label": "Dental veneers", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "<PERSON><PERSON><PERSON><PERSON>", "optionValue": "1"}, {"optionText": "Lu<PERSON><PERSON> Veneers", "optionValue": "2"}, {"optionText": "Composite Veneers", "optionValue": "3"}], "hidden": null, "text": "Dropdown list"}, {"step": 1, "unit": 1, "sign": "", "label": "Number Of Teeth", "_id": 1, "default": "", "minValue": "1", "maxValue": "34", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_1", "desc_option": "after", "required": false, "hidden": null, "text": "Basic slider"}, {"label": "<PERSON><PERSON>", "_id": 2, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_2", "desc_option": "after", "options": [{"optionText": "Professional", "optionValue": "1"}, {"optionText": "Dentist Dispense at-home System", "optionValue": "2"}], "hidden": null, "text": "Dropdown list"}, {"step": 1, "unit": 1, "sign": "", "label": "Number Of Sessions", "_id": 4, "default": "", "minValue": "1", "maxValue": "8", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_13", "desc_option": "after", "stm_dublicate_field_id": 1, "required": false, "hidden": null, "text": "Basic slider"}, {"_id": 3, "label": "Diagnostic X-Ray", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_3", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "80", "optionHint": ""}], "text": "Switch toggle", "hidden": null}, {"label": "Dental Fillings", "_id": 5, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_5", "desc_option": "after", "options": [{"optionText": "Front Teeth dental bonding", "optionValue": "1"}, {"optionText": "Back Teeth dental bonding", "optionValue": "2"}, {"optionText": "Dental amaglam", "optionValue": "3"}], "text": "Dropdown list", "hidden": null}, {"step": 1, "unit": 1, "sign": "", "label": "Number Of Teeth", "_id": 7, "default": "", "minValue": "1", "maxValue": "34", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_16", "desc_option": "after", "required": false, "hidden": null, "stm_dublicate_field_id": 1, "text": "Basic slider"}, {"label": "Crowns & Bridges", "_id": 6, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_6", "desc_option": "after", "options": [{"optionText": "Dental Bridges", "optionValue": "1"}, {"optionText": "Conventional Dental Bridges", "optionValue": "2"}, {"optionText": "Maryland Bridges", "optionValue": "3"}], "text": "Dropdown list", "hidden": null}, {"step": 1, "unit": 1, "sign": "", "label": "Number Of Teeth", "_id": 9, "default": "", "minValue": "1", "maxValue": "34", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_18", "desc_option": "after", "required": false, "hidden": null, "stm_dublicate_field_id": 1, "text": "Basic slider"}, {"label": "Dentures", "_id": 8, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_8", "desc_option": "after", "options": [{"optionText": "Complete", "optionValue": "1"}, {"optionText": "Partial", "optionValue": "2"}], "text": "Dropdown list", "hidden": null}, {"step": 1, "unit": 1, "sign": "", "label": "Number Of Teeth", "_id": 10, "default": "", "minValue": "1", "maxValue": "34", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_19", "desc_option": "after", "required": false, "hidden": null, "stm_dublicate_field_id": 1, "text": "Basic slider"}, {"_id": 11, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " ( dropDown_field_id_0 * range_field_id_1 ) + ( dropDown_field_id_2 * range_field_id_13 ) + toggle_field_id_3 + ( dropDown_field_id_5 * range_field_id_16 ) + ( dropDown_field_id_6 * range_field_id_18 ) + ( dropDown_field_id_8 * range_field_id_19 ) ", "additionalStyles": "", "alias": "total_field_id_11", "icon": "ccb-icon-Path-3516", "label": "Total description", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 11, "alias": "total_field_id_11", "label": "Total description", "hidden": null, "formula": " ( dropDown_field_id_0 * range_field_id_1 ) + ( dropDown_field_id_2 * range_field_id_13 ) + toggle_field_id_3 + ( dropDown_field_id_5 * range_field_id_16 ) + ( dropDown_field_id_6 * range_field_id_18 ) + ( dropDown_field_id_8 * range_field_id_19 ) ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_11", "total": 0, "converted": "$ 0.00", "label": "Total description", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 7}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Delivery Service", "ccb_fields": [{"unit": "1", "label": "Height", "_id": "0", "default": "5", "description": "[centimeters]", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_0", "text": "Quantity field", "required": false, "hidden": null}, {"unit": "1", "label": "<PERSON><PERSON><PERSON>", "_id": "1", "default": "40", "description": "[centimeters]", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_1", "text": "Quantity field", "required": false, "hidden": null}, {"unit": "1", "label": "De<PERSON><PERSON>", "_id": "2", "default": "20", "description": "centimeters", "placeholder": "", "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_2", "text": "Quantity field", "required": false, "hidden": null}, {"step": "1", "unit": "1", "sign": "miles", "label": "Distance", "default": "15", "_id": "3", "minValue": "0", "maxValue": "50", "description": "[miles]", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": "", "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_3", "text": "Basic slider", "required": false, "hidden": null}, {"label": "Type of Service", "_id": 6, "default": "", "onValue": null, "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "addToSummary": true, "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_6", "desc_option": "after", "summary_view": "show_value", "styles": {"box_style": "vertical", "style": "default"}, "options": [{"optionText": "Within 2 working days", "optionValue": "30"}, {"optionText": "Door to door", "optionValue": "50"}, {"optionText": "Same day", "optionValue": "100"}], "apply_style_for_all": false, "text": "Radio select", "hidden": null}, {"_id": "5", "symbol": "$", "type": "Total", "_tag": "cost-total", "costCalcFormula": "if( ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3) { ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3 + radio_field_id_6 }", "additionalStyles": "", "icon": "ccb-icon-Path-3516", "alias": "total_field_id_5", "label": "Total description", "text": "Formula"}], "ccb_formula": [{"id": "5", "label": "Total description", "hidden": false, "formula": "if( ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3) { ( quantity_field_id_0 * 0.5 + quantity_field_id_1 * 0.5 + quantity_field_id_2 * 0.5 ) * range_field_id_3 + radio_field_id_6 }", "additionalStyles": "", "data": {"total": 487.5, "converted": "$ 487,50", "label": "Total description", "hidden": false, "additionalStyles": ""}, "total": 487.5}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": "2", "decimal_separator": ",", "thousands_separator": ".", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Total: [ccb-total-0]", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 6}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Car Wash", "ccb_fields": [{"label": "Basic Wash", "_id": 0, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_0", "desc_option": "after", "options": [{"optionText": "Express Wash", "optionValue": "100"}, {"optionText": "Express Wash, plus Tires", "optionValue": "250"}, {"optionText": "Express Wash, plus Tires, plus Interior Clean", "optionValue": "400"}], "hidden": null, "text": "Dropdown list"}, {"label": "Car Type", "_id": 1, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_1", "desc_option": "after", "options": [{"optionText": "Regular Car", "optionValue": "60"}, {"optionText": "<PERSON>", "optionValue": "80"}, {"optionText": "SUV or Truck", "optionValue": "90"}], "hidden": null, "text": "Dropdown list"}, {"_id": 2, "label": "Wheel Cleaning", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_2", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "1", "optionHint": "Yes"}], "hidden": null, "text": "Switch toggle"}, {"step": 1, "unit": "5", "sign": "", "label": "Number Of Wheels", "_id": 3, "default": "", "minValue": "0", "maxValue": "4", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": true, "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_3", "desc_option": "after", "required": false, "hidden": null, "text": "Basic slider"}, {"label": "Protection Type", "_id": 4, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_4", "desc_option": "after", "options": [{"optionText": "Clear-Coat Protectors", "optionValue": "400"}, {"optionText": "Temporary Wax", "optionValue": "150"}], "hidden": null, "text": "Dropdown list"}, {"label": "Polish Type", "_id": 5, "default": "", "description": "", "required": false, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_5", "desc_option": "after", "options": [{"optionText": "Regular", "optionValue": "50"}, {"optionText": "Full", "optionValue": "80"}], "hidden": null, "text": "Dropdown list"}, {"_id": 6, "label": "Underbody Wash", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_6", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "500", "optionHint": "Yes"}], "hidden": null, "text": "Switch toggle"}, {"_id": 7, "label": "<PERSON><PERSON>", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_7", "desc_option": "after", "options": [{"optionText": "Yes", "optionValue": "20", "optionHint": "Yes"}], "hidden": null, "text": "Switch toggle"}, {"_id": 8, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " dropDown_field_id_0 + dropDown_field_id_1 + ( toggle_field_id_2 * range_field_id_3 ) + dropDown_field_id_4 + dropDown_field_id_5 + toggle_field_id_6 + toggle_field_id_7 ", "additionalStyles": "", "alias": "total_field_id_8", "icon": "ccb-icon-Path-3516", "label": "Total Summary", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 8, "alias": "total_field_id_8", "label": "Total Summary", "hidden": null, "formula": " dropDown_field_id_0 + dropDown_field_id_1 + ( toggle_field_id_2 * range_field_id_3 ) + dropDown_field_id_4 + dropDown_field_id_5 + toggle_field_id_6 + toggle_field_id_7 ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_8", "total": 0, "converted": "$ 0.00", "label": "Total Summary", "hidden": null, "additionalStyles": ""}, "total": 0}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Summary block", "tour_description": "Edit the default view of the Total summary.", "header_title": "Summary", "descriptions": true, "hide_empty": true, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-new-calculator", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": 2, "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left_with_space", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Warning texts", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Order Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following:\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total Summary"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "title": "Untitled", "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 5}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Calculate Your Taxes", "ccb_fields": [{"label": "HTML (0)", "_id": 0, "_event": "", "type": "Html", "htmlContent": "<p style=\"font-size: 16px; color: black; line-height: 26px;\">Efficiently unleash cross-media information without cross-media value. Quickly maximize timely deliverables.</p>", "placeholder": "", "hidden": null, "_tag": "cost-html", "additionalCss": "", "additionalStyles": "", "icon": "ccb-icon-Path-3517", "alias": "html_field_id_0", "text": "Html element", "required": false}, {"label": "Income Year", "_id": 9, "default": "2022_0", "description": "", "required": true, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": "", "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_9", "desc_option": "after", "options": [{"optionText": "2021–2022", "optionValue": "2022"}, {"optionText": "2020–2021", "optionValue": "2021"}, {"optionText": "2019–2020", "optionValue": "2020"}, {"optionText": "2018–2019", "optionValue": "2019"}, {"optionText": "2017-2018", "optionValue": "2018"}, {"optionText": "2016–2017", "optionValue": "2017"}, {"optionText": "2015–2016", "optionValue": "2016"}, {"optionText": "2014–2015", "optionValue": "2015"}, {"optionText": "2013–2014", "optionValue": "2014"}, {"optionText": "2012–2013", "optionValue": "2013"}], "text": "Dropdown list", "hidden": null}, {"label": "Residency Status", "_id": 3, "default": "4800_2", "onValue": null, "description": "", "required": true, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_3", "desc_option": "after", "options": [{"optionText": "Non-resident", "optionValue": "0"}, {"optionText": "Part-year", "optionValue": "2400"}, {"optionText": "Full year", "optionValue": "4800"}], "text": "Radio select", "hidden": null}, {"unit": 1, "label": "Income from Salary ($)", "_id": 2, "default": "124000", "description": "", "placeholder": "", "required": true, "_event": "keyup", "type": "Quantity", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "enabled_currency_settings": false, "_tag": "cost-quantity", "icon": "ccb-icon-Subtraction-6", "alias": "quantity_field_id_2", "desc_option": "after", "step": 1, "text": "Quantity field", "hidden": null}, {"_id": 5, "label": "Type of service", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_5", "desc_option": "after", "options": [{"optionText": "Finance Advice", "optionValue": "500", "optionHint": ""}, {"optionText": "Law Services", "optionValue": "500", "optionHint": ""}, {"optionText": "Accounting Services", "optionValue": "500", "optionHint": ""}, {"optionText": "Business Planning", "optionValue": "500", "optionHint": ""}], "text": "Checkbox", "hidden": null}, {"label": "Business Entity", "_id": 6, "default": "1500_0", "description": "", "required": true, "_event": "change", "type": "Drop Down", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-drop-down", "icon": "ccb-icon-dropdown-2", "alias": "dropDown_field_id_6", "desc_option": "after", "options": [{"optionText": "Holding Company", "optionValue": "1500"}], "text": "Dropdown list", "hidden": null}, {"_id": 7, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": "(quantity_field_id_2 * 0.1) + radio_field_id_3 + checkbox_field_id_5 + dropDown_field_id_6", "additionalStyles": "", "alias": "total_field_id_7", "icon": "ccb-icon-Path-3516", "label": "Total", "totalSymbol": false, "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 7, "alias": "total_field_id_7", "label": "Total", "hidden": null, "formula": "(quantity_field_id_2 * 0.1) + radio_field_id_3 + checkbox_field_id_5 + dropDown_field_id_6", "additionalStyles": "", "totalSymbol": false, "totalSymbolSign": "", "data": {"alias": "total_field_id_7", "total": 18700, "converted": "$18,700", "label": "Total", "hidden": null, "additionalStyles": ""}, "total": 18700}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Grand Total", "tour_description": "Edit the default view of the Total summary.", "header_title": "Total Summary", "descriptions": true, "hide_empty": false, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-Union-28", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": "0", "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Notifications", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Send Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following=>\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 7}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}, {"ccb_name": "Internet Marketing", "ccb_fields": [{"label": "Write fresh content", "_event": "", "_id": 0, "description": "", "placeholder": "Your Content", "_tag": "cost-text", "type": "Text Area", "additionalCss": "", "additionalStyles": "", "icon": "ccb-icon-Subtraction-7", "desc_option": "after", "alias": "text_field_id_0", "text": "Text field", "required": false, "hidden": null}, {"label": "Company size", "_id": 1, "default": "1200_1", "onValue": null, "description": "", "required": true, "_event": "change", "allowRound": "", "additionalCss": "", "_tag": "cost-radio", "additionalStyles": "", "allowCurrency": true, "type": "Radio Button", "icon": "ccb-icon-Path-3511", "alias": "radio_field_id_1", "desc_option": "after", "options": [{"optionText": "Small", "optionValue": "360"}, {"optionText": "Medium", "optionValue": "1200"}, {"optionText": "Large", "optionValue": "3600"}, {"optionText": "Corporate", "optionValue": "7200"}, {"optionText": "Conglomerate", "optionValue": "12000"}], "text": "Radio select", "hidden": null}, {"step": "1", "unit": "10", "sign": "", "label": "Number of website pages", "_id": 2, "default": "10", "minValue": 0, "maxValue": "100", "description": "", "_event": "change", "additionalCss": "", "allowRound": "", "_tag": "cost-range", "additionalStyles": "", "allowCurrency": true, "type": "Range Button", "icon": "ccb-icon-Union-5", "alias": "range_field_id_2", "desc_option": "after", "text": "Basic slider", "required": false, "hidden": null}, {"_id": 3, "label": "Marketing", "type": "Toggle", "description": "", "required": false, "_event": "change", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-toggle", "icon": "ccb-icon-Path-3515", "alias": "toggle_field_id_3", "desc_option": "after", "options": [{"optionText": "Buy PPC (Pay per Click) Ads", "optionValue": "500", "optionHint": ""}, {"optionText": "Work on your presence", "optionValue": "1000", "optionHint": ""}], "text": "Switch toggle", "hidden": null}, {"_id": 4, "label": "Annual Operating Costs", "description": "", "required": false, "_event": "change", "type": "Checkbox", "allowRound": "", "additionalCss": "", "additionalStyles": "", "allowCurrency": true, "_tag": "cost-checkbox", "icon": "ccb-icon-Path-3512", "alias": "checkbox_field_id_4", "desc_option": "after", "options": [{"optionText": "Advertising & Marketing", "optionValue": "500", "optionHint": ""}, {"optionText": "Equipment & Hardware", "optionValue": "500", "optionHint": ""}, {"optionText": "Training & Education", "optionValue": "500", "optionHint": ""}, {"optionText": "Professional Fees", "optionValue": "500", "optionHint": ""}, {"optionText": "Insurance", "optionValue": "500", "optionHint": ""}], "text": "Checkbox", "hidden": null}, {"_id": 6, "currency": "$", "type": "Total", "additionalCss": "", "_tag": "cost-total", "costCalcFormula": " radio_field_id_1 + range_field_id_2 + toggle_field_id_3 + checkbox_field_id_4 ", "additionalStyles": "", "alias": "total_field_id_6", "icon": "ccb-icon-Path-3516", "label": "Total", "totalSymbol": "", "totalSymbolSign": "", "default": "", "text": "Formula"}], "ccb_formula": [{"id": 6, "alias": "total_field_id_6", "label": "Total", "hidden": null, "formula": " radio_field_id_1 + range_field_id_2 + toggle_field_id_3 + checkbox_field_id_4 ", "additionalStyles": "", "totalSymbol": "", "totalSymbolSign": "", "data": {"alias": "total_field_id_6", "total": 1210, "converted": "$1,210", "label": "Total", "hidden": null, "additionalStyles": ""}, "total": 1210}], "ccb_category": "", "ccb_icon": "", "ccb_type": "", "ccb_link": "", "ccb_info": "", "ccb_description": "", "ccb_conditions": {"nodes": [], "links": []}, "ccb_form_settings": {"general": {"tour_title": "Grand Total", "tour_description": "Edit the default view of the Total summary.", "header_title": "Total Summary", "descriptions": true, "hide_empty": false, "hide_empty_for_orders_pdf_emails": true, "sticky": false, "show_option_unit": false, "in_pro": false, "icons": "ccb-icon-Union-28", "slug": "total-summary", "styles": {"toggle": "", "checkbox": "", "radio": "", "checkbox_with_img": "", "radio_with_img": ""}}, "currency": {"tour_title": "<PERSON><PERSON><PERSON><PERSON>", "tour_description": "Set the currency sign and edit its default appearance.", "currency": "$", "num_after_integer": "0", "decimal_separator": ".", "thousands_separator": ",", "currencyPosition": "left", "in_pro": false, "icons": "ccb-icon-Union-23", "slug": "currency"}, "texts": {"tour_title": "Notifications", "tour_description": "Manage notifications of Calculator forms.", "title": "Your service request has been completed!", "description": "We have sent order details to your email.", "issued_on": "Issued on", "reset_btn": "Create new calculation", "invoice_btn": "Get invoice", "required_msg": "This field is required", "in_pro": false, "icons": "ccb-icon-Path-3601", "slug": "texts", "form_fields": {"email_format": "Invalid email", "email_field": "Email is required", "name_field": "Name is required", "phone_field": "Phone number is required"}}, "thankYouPage": {"type": "same_page", "page_id": "", "custom_page_link": "", "title": "Thank you for your order!", "description": "We have sent order details to your email.", "order_title": "Order ID", "back_button_text": "Back to calculator", "download_button": false, "download_button_text": "Download PDF", "share_button": false, "share_button_text": "Send PDF to", "custom_button": false, "custom_button_text": "Go to website", "custom_button_link": "http://test-cost-calc.local"}, "formFields": {"tour_title": "Send Form", "tour_description": "Choose contact form type and fill out settings.", "fields": [], "formulas": [], "emailSubject": "", "contactFormId": "", "accessEmail": "", "adminEmailAddress": "", "customEmailAddresses": [], "submitBtnText": "Submit", "openModalBtnText": "Make order", "allowContactForm": "", "body": "Dear sir/madam\nWe would be very grateful to you if you could provide us the quotation of the following=>\n\nTotal Summary\n[ccb-subtotal]\nTotal: [ccb-total-0]\nLooking forward to hearing back from you.\nThanks in advance", "payment": "", "paymentMethod": "", "paymentMethods": [], "in_pro": true, "icons": "ccb-icon-XMLID_426", "slug": "send-form"}, "sendFormFields": [{"name": "name", "required": true, "value": ""}, {"name": "email", "required": true, "value": ""}, {"name": "phone", "required": true, "value": ""}, {"name": "message", "required": false, "value": ""}], "sendFormRequires": [{"required": false}, {"required": false}, {"required": false}, {"required": false}], "woo_products": {"tour_title": "Woo Products", "tour_description": "Enables Calculator on the product page.", "enable": "", "category_id": "", "category_ids": [""], "hook_to_show": "woocommerce_after_single_product_summary", "hide_woo_cart": "", "meta_links": [], "in_pro": true, "icons": "ccb-icon-Union-17", "slug": "woo-products"}, "woo_checkout": {"tour_title": "Woo Checkout", "tour_description": "Enables WooCommerce Checkout.", "enable": "", "product_id": "", "redirect_to": "cart", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total description"}], "in_pro": true, "icons": "ccb-icon-Path-3498", "slug": "woo-checkout", "replace_product": true}, "stripe": {"tour_title": "Stripe", "tour_description": "Enables Stripe payment gateway.", "enable": "", "secretKey": "", "publishKey": "", "currency": "usd", "description": "[ccb-total-0]", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3499", "slug": "stripe"}, "paypal": {"tour_title": "PayPal", "tour_description": "Enables PayPal payment gateway.", "enable": "", "description": "[ccb-total-0]", "paypal_email": "", "currency_code": "", "paypal_mode": "sandbox", "formulas": [{"idx": 0, "title": "Total"}], "in_pro": true, "icons": "ccb-icon-Path-3500", "slug": "paypal"}, "webhooks": {"tour_title": "Webhooks", "tour_description": "Enables custom webhooks for different events.", "enableSendForms": false, "enablePaymentBtn": false, "enableEmailQuote": false, "send_form_url": "", "payment_btn_url": "", "email_quote_url": "", "secret_key_send_form": "", "secret_key_payment_btn": "", "secret_key_email_quote": "", "in_pro": true, "icons": "ccb-icon-Webhooks", "slug": "webhooks"}, "recaptcha": {"enable": "", "type": "v2", "v2": {"siteKey": "", "secretKey": ""}, "v3": {"siteKey": "", "secretKey": ""}, "options": {"v2": "Google reCAPTCHA v2", "v3": "Google reCAPTCHA v3"}, "use_in_all": false}, "notice": {"requiredField": "This field is required"}, "icon": "fas fa-cogs", "type": "Cost Calculator Settings", "calc_id": 6}, "ccb_preset": {"title": "<PERSON><PERSON><PERSON>", "key": "default", "image": "http://test-cost-calc.local/wp-content/plugins/cost-calculator-free-v3/frontend/dist/img/appearance/theme-default.png", "data": {"desktop": {"colors": {"container": {"color": "#ffffff", "blur": 0, "opacity": 100}, "primary_color": "#001931", "accent_color": "#00B163", "secondary_color": "#FFFFFF", "error_color": "#D94141", "svg_color": 0}, "layout": {"box_style": "vertical"}, "typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 16, "summary_header_font_weight": 700, "label_font_size": 16, "label_font_weight": "bold", "description_font_size": 14, "description_font_weight": 400, "total_field_font_size": 16, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 18, "total_font_weight": "bold", "fields_btn_font_size": 16, "fields_btn_font_weight": 500}, "borders": {"container_border": {"type": "solid", "width": 0, "radius": 0}, "fields_border": {"type": "solid", "width": 2, "radius": 4}, "button_border": {"type": "solid", "width": 2, "radius": 4}}, "shadows": {"container_shadow": {"color": "#ffffff", "blur": 0, "x": 0, "y": 0}}, "elements_sizes": {"field_and_buttons_height": 45, "container_vertical_max_width": 970, "container_horizontal_max_width": 970, "container_two_column_max_width": 1200}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [40, 40, 40, 40], "description_position": "after"}, "others": {"calc_preloader": 0}}, "mobile": {"typography": {"header_font_size": 18, "header_font_weight": "bold", "summary_header_size": 14, "summary_header_font_weight": 700, "label_font_size": 11, "label_font_weight": "bold", "description_font_size": 11, "description_font_weight": 500, "total_field_font_size": 14, "total_text_transform": "capitalize", "total_field_font_weight": 500, "total_font_size": 16, "total_font_weight": "bold", "fields_btn_font_size": 14, "fields_btn_font_weight": 500}, "elements_sizes": {"field_and_buttons_height": 45}, "spacing_and_positions": {"field_side_indents": 20, "field_spacing": 20, "container_margin": [0, 0, 0, 0], "container_padding": [25, 25, 25, 25]}}}}, "ccb_preset_key": "default"}], "categories": []}